import { O as isString, d as defineComponent, Z as useAttrs, ae as useSlots, c as computed, s as shallowRef, r as ref, w as watch, n as nextTick, z as onMounted, t as toRef, b as openBlock, m as createElementBlock, D as createCommentVNode, F as Fragment, k as normalizeClass, u as unref, B as renderSlot, j as createBaseVNode, e as createBlock, f as withCtx, C as resolveDynamicComponent, _ as mergeProps, p as createVNode, I as withModifiers, aj as NOOP, H as toDisplayString, l as normalizeStyle, E as isObject, g as getCurrentInstance, ah as hasOwn, a as inject, M as watchEffect, R as renderList, x as reactive, q as provide, h as withDirectives, S as withKeys, G as createTextVNode, v as vShow, a2 as storeToRefs, o as onBeforeUnmount, ap as createStaticVNode, az as createSlots, Q as resolveDirective } from "./bootstrap.ab073eb8.js";
import { u as useThemeStore } from "./theme.55a1df39.js";
import { W as WhiteboardThemeEnum, u as useWhiteboardStore, D as DEFAULT_BRUSH_SIZE, w as whiteboardThemeMap, a as WhiteboardColorEnum, b as WhiteboardSizeEnum, i as isHasChildTools, c as DEFAULT_ERASER_HEIGHT } from "./whiteboard.c3e5eabe.js";
import { I as IHistoryPluginEvents, a as IPencilModes, P as PencilPlugin, M as MultiPencilPlugin, b as EraserPlugin, S as SelectionPlugin, L as LinePlugin, A as ArrowPlugin, C as CirclePlugin, c as EllipsePlugin, R as RectPlugin, T as TrianglePlugin, d as RightTrianglePlugin } from "./hlwhiteboard.b54f17ff.js";
import { _ as _export_sfc$1, l as logger } from "./index.9b9bd033.js";
import { d as buildProps, e as definePropType, u as useNamespace, a6 as view_default, a7 as hide_default, E as ElIcon, k as circle_close_default, _ as _export_sfc, w as withInstall, j as useLocale, f as addUnit, l as arrow_down_default, h as close_default } from "./base.676dddc3.js";
import { u as useTooltipContentProps, C as ClickOutside, c as ElTooltip } from "./index.8e8e3f71.js";
import { E as ElPopover } from "./el-popover.f265f19e.js";
import { S as SvgIcon } from "./index.5cc837e0.js";
import { e as useFocusController, f as useComposition, h as useDraggable, i as useParentElement } from "./main.667b0435.js";
import { E as ElButton } from "./el-button.a9e8e4ae.js";
import { m as mutable, a as useResizeObserver, d as debounce } from "./typescript.063380fa.js";
import { i as isClient, a as isNumber, g as isNil } from "./index.1c1fd1ce.js";
import { f as useSizeProp, i as iconPropType, b as useFormItem, d as useFormItemInputId, u as useFormSize, k as useFormDisabled, V as ValidateComponentsMap } from "./use-form-common-props.6b0d7cd2.js";
import { u as useAriaProps } from "./index.4d07c967.js";
import { U as UPDATE_MODEL_EVENT, d as debugWarn, C as CHANGE_EVENT } from "./event.183fce42.js";
import { u as useAttrs$1, g as getClientXY } from "./position.2e4d825a.js";
import { E as EVENT_CODE } from "./index.4e3d2b08.js";
import "./axios.b90ffefd.js";
import "./base.649d38c6.js";
import "./toast.4a39bbb3.js";
import "./crypto-js.7319a219.js";
import "./encryptlong.f30353e7.js";
import "./toastWidget.501568b8.js";
import "./IComm.f4ebabd4.js";
import "./isUndefined.a6a5e481.js";
import "./stopDrag.9e5623d9.js";
import "./index.382e5561.js";
import "./school.662f4ee7.js";
/* empty css                     */import "./directive.aac8de61.js";
import "./classroom.421c4d02.js";
import "./bury.6b12311b.js";
const isFirefox = () => isClient && /firefox/i.test(window.navigator.userAgent);
let hiddenTextarea = void 0;
const HIDDEN_STYLE = {
  height: "0",
  visibility: "hidden",
  overflow: isFirefox() ? "" : "hidden",
  position: "absolute",
  "z-index": "-1000",
  top: "0",
  right: "0"
};
const CONTEXT_STYLE = [
  "letter-spacing",
  "line-height",
  "padding-top",
  "padding-bottom",
  "font-family",
  "font-weight",
  "font-size",
  "text-rendering",
  "text-transform",
  "width",
  "text-indent",
  "padding-left",
  "padding-right",
  "border-width",
  "box-sizing"
];
function calculateNodeStyling(targetElement) {
  const style = window.getComputedStyle(targetElement);
  const boxSizing = style.getPropertyValue("box-sizing");
  const paddingSize = Number.parseFloat(style.getPropertyValue("padding-bottom")) + Number.parseFloat(style.getPropertyValue("padding-top"));
  const borderSize = Number.parseFloat(style.getPropertyValue("border-bottom-width")) + Number.parseFloat(style.getPropertyValue("border-top-width"));
  const contextStyle = CONTEXT_STYLE.map((name) => [
    name,
    style.getPropertyValue(name)
  ]);
  return { contextStyle, paddingSize, borderSize, boxSizing };
}
function calcTextareaHeight(targetElement, minRows = 1, maxRows) {
  var _a;
  if (!hiddenTextarea) {
    hiddenTextarea = document.createElement("textarea");
    document.body.appendChild(hiddenTextarea);
  }
  const { paddingSize, borderSize, boxSizing, contextStyle } = calculateNodeStyling(targetElement);
  contextStyle.forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value));
  Object.entries(HIDDEN_STYLE).forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value, "important"));
  hiddenTextarea.value = targetElement.value || targetElement.placeholder || "";
  let height = hiddenTextarea.scrollHeight;
  const result = {};
  if (boxSizing === "border-box") {
    height = height + borderSize;
  } else if (boxSizing === "content-box") {
    height = height - paddingSize;
  }
  hiddenTextarea.value = "";
  const singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;
  if (isNumber(minRows)) {
    let minHeight = singleRowHeight * minRows;
    if (boxSizing === "border-box") {
      minHeight = minHeight + paddingSize + borderSize;
    }
    height = Math.max(minHeight, height);
    result.minHeight = `${minHeight}px`;
  }
  if (isNumber(maxRows)) {
    let maxHeight = singleRowHeight * maxRows;
    if (boxSizing === "border-box") {
      maxHeight = maxHeight + paddingSize + borderSize;
    }
    height = Math.min(maxHeight, height);
  }
  result.height = `${height}px`;
  (_a = hiddenTextarea.parentNode) == null ? void 0 : _a.removeChild(hiddenTextarea);
  hiddenTextarea = void 0;
  return result;
}
const inputProps = buildProps({
  id: {
    type: String,
    default: void 0
  },
  size: useSizeProp,
  disabled: Boolean,
  modelValue: {
    type: definePropType([
      String,
      Number,
      Object
    ]),
    default: ""
  },
  maxlength: {
    type: [String, Number]
  },
  minlength: {
    type: [String, Number]
  },
  type: {
    type: String,
    default: "text"
  },
  resize: {
    type: String,
    values: ["none", "both", "horizontal", "vertical"]
  },
  autosize: {
    type: definePropType([Boolean, Object]),
    default: false
  },
  autocomplete: {
    type: String,
    default: "off"
  },
  formatter: {
    type: Function
  },
  parser: {
    type: Function
  },
  placeholder: {
    type: String
  },
  form: {
    type: String
  },
  readonly: Boolean,
  clearable: Boolean,
  showPassword: Boolean,
  showWordLimit: Boolean,
  suffixIcon: {
    type: iconPropType
  },
  prefixIcon: {
    type: iconPropType
  },
  containerRole: {
    type: String,
    default: void 0
  },
  tabindex: {
    type: [String, Number],
    default: 0
  },
  validateEvent: {
    type: Boolean,
    default: true
  },
  inputStyle: {
    type: definePropType([Object, Array, String]),
    default: () => mutable({})
  },
  autofocus: Boolean,
  rows: {
    type: Number,
    default: 2
  },
  ...useAriaProps(["ariaLabel"])
});
const inputEmits = {
  [UPDATE_MODEL_EVENT]: (value) => isString(value),
  input: (value) => isString(value),
  change: (value) => isString(value),
  focus: (evt) => evt instanceof FocusEvent,
  blur: (evt) => evt instanceof FocusEvent,
  clear: () => true,
  mouseleave: (evt) => evt instanceof MouseEvent,
  mouseenter: (evt) => evt instanceof MouseEvent,
  keydown: (evt) => evt instanceof Event,
  compositionstart: (evt) => evt instanceof CompositionEvent,
  compositionupdate: (evt) => evt instanceof CompositionEvent,
  compositionend: (evt) => evt instanceof CompositionEvent
};
function useCursor(input) {
  let selectionInfo;
  function recordCursor() {
    if (input.value == void 0)
      return;
    const { selectionStart, selectionEnd, value } = input.value;
    if (selectionStart == null || selectionEnd == null)
      return;
    const beforeTxt = value.slice(0, Math.max(0, selectionStart));
    const afterTxt = value.slice(Math.max(0, selectionEnd));
    selectionInfo = {
      selectionStart,
      selectionEnd,
      value,
      beforeTxt,
      afterTxt
    };
  }
  function setCursor() {
    if (input.value == void 0 || selectionInfo == void 0)
      return;
    const { value } = input.value;
    const { beforeTxt, afterTxt, selectionStart } = selectionInfo;
    if (beforeTxt == void 0 || afterTxt == void 0 || selectionStart == void 0)
      return;
    let startPos = value.length;
    if (value.endsWith(afterTxt)) {
      startPos = value.length - afterTxt.length;
    } else if (value.startsWith(beforeTxt)) {
      startPos = beforeTxt.length;
    } else {
      const beforeLastChar = beforeTxt[selectionStart - 1];
      const newIndex = value.indexOf(beforeLastChar, selectionStart - 1);
      if (newIndex !== -1) {
        startPos = newIndex + 1;
      }
    }
    input.value.setSelectionRange(startPos, startPos);
  }
  return [recordCursor, setCursor];
}
const __default__$2 = defineComponent({
  name: "ElInput",
  inheritAttrs: false
});
const _sfc_main$c = /* @__PURE__ */ defineComponent({
  ...__default__$2,
  props: inputProps,
  emits: inputEmits,
  setup(__props, { expose, emit }) {
    const props = __props;
    const rawAttrs = useAttrs();
    const attrs = useAttrs$1();
    const slots = useSlots();
    const containerKls = computed(() => [
      props.type === "textarea" ? nsTextarea.b() : nsInput.b(),
      nsInput.m(inputSize.value),
      nsInput.is("disabled", inputDisabled.value),
      nsInput.is("exceed", inputExceed.value),
      {
        [nsInput.b("group")]: slots.prepend || slots.append,
        [nsInput.m("prefix")]: slots.prefix || props.prefixIcon,
        [nsInput.m("suffix")]: slots.suffix || props.suffixIcon || props.clearable || props.showPassword,
        [nsInput.bm("suffix", "password-clear")]: showClear.value && showPwdVisible.value,
        [nsInput.b("hidden")]: props.type === "hidden"
      },
      rawAttrs.class
    ]);
    const wrapperKls = computed(() => [
      nsInput.e("wrapper"),
      nsInput.is("focus", isFocused.value)
    ]);
    const { form: elForm, formItem: elFormItem } = useFormItem();
    const { inputId } = useFormItemInputId(props, {
      formItemContext: elFormItem
    });
    const inputSize = useFormSize();
    const inputDisabled = useFormDisabled();
    const nsInput = useNamespace("input");
    const nsTextarea = useNamespace("textarea");
    const input = shallowRef();
    const textarea = shallowRef();
    const hovering = ref(false);
    const passwordVisible = ref(false);
    const countStyle = ref();
    const textareaCalcStyle = shallowRef(props.inputStyle);
    const _ref = computed(() => input.value || textarea.value);
    const { wrapperRef, isFocused, handleFocus, handleBlur } = useFocusController(_ref, {
      beforeFocus() {
        return inputDisabled.value;
      },
      afterBlur() {
        var _a;
        if (props.validateEvent) {
          (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, "blur").catch((err) => debugWarn());
        }
      }
    });
    const needStatusIcon = computed(() => {
      var _a;
      return (_a = elForm == null ? void 0 : elForm.statusIcon) != null ? _a : false;
    });
    const validateState = computed(() => (elFormItem == null ? void 0 : elFormItem.validateState) || "");
    const validateIcon = computed(() => validateState.value && ValidateComponentsMap[validateState.value]);
    const passwordIcon = computed(() => passwordVisible.value ? view_default : hide_default);
    const containerStyle = computed(() => [
      rawAttrs.style
    ]);
    const textareaStyle = computed(() => [
      props.inputStyle,
      textareaCalcStyle.value,
      { resize: props.resize }
    ]);
    const nativeInputValue = computed(() => isNil(props.modelValue) ? "" : String(props.modelValue));
    const showClear = computed(() => props.clearable && !inputDisabled.value && !props.readonly && !!nativeInputValue.value && (isFocused.value || hovering.value));
    const showPwdVisible = computed(() => props.showPassword && !inputDisabled.value && !!nativeInputValue.value && (!!nativeInputValue.value || isFocused.value));
    const isWordLimitVisible = computed(() => props.showWordLimit && !!props.maxlength && (props.type === "text" || props.type === "textarea") && !inputDisabled.value && !props.readonly && !props.showPassword);
    const textLength = computed(() => nativeInputValue.value.length);
    const inputExceed = computed(() => !!isWordLimitVisible.value && textLength.value > Number(props.maxlength));
    const suffixVisible = computed(() => !!slots.suffix || !!props.suffixIcon || showClear.value || props.showPassword || isWordLimitVisible.value || !!validateState.value && needStatusIcon.value);
    const [recordCursor, setCursor] = useCursor(input);
    useResizeObserver(textarea, (entries) => {
      onceInitSizeTextarea();
      if (!isWordLimitVisible.value || props.resize !== "both")
        return;
      const entry = entries[0];
      const { width } = entry.contentRect;
      countStyle.value = {
        right: `calc(100% - ${width + 15 + 6}px)`
      };
    });
    const resizeTextarea = () => {
      const { type, autosize } = props;
      if (!isClient || type !== "textarea" || !textarea.value)
        return;
      if (autosize) {
        const minRows = isObject(autosize) ? autosize.minRows : void 0;
        const maxRows = isObject(autosize) ? autosize.maxRows : void 0;
        const textareaStyle2 = calcTextareaHeight(textarea.value, minRows, maxRows);
        textareaCalcStyle.value = {
          overflowY: "hidden",
          ...textareaStyle2
        };
        nextTick(() => {
          textarea.value.offsetHeight;
          textareaCalcStyle.value = textareaStyle2;
        });
      } else {
        textareaCalcStyle.value = {
          minHeight: calcTextareaHeight(textarea.value).minHeight
        };
      }
    };
    const createOnceInitResize = (resizeTextarea2) => {
      let isInit = false;
      return () => {
        var _a;
        if (isInit || !props.autosize)
          return;
        const isElHidden = ((_a = textarea.value) == null ? void 0 : _a.offsetParent) === null;
        if (!isElHidden) {
          resizeTextarea2();
          isInit = true;
        }
      };
    };
    const onceInitSizeTextarea = createOnceInitResize(resizeTextarea);
    const setNativeInputValue = () => {
      const input2 = _ref.value;
      const formatterValue = props.formatter ? props.formatter(nativeInputValue.value) : nativeInputValue.value;
      if (!input2 || input2.value === formatterValue)
        return;
      input2.value = formatterValue;
    };
    const handleInput = async (event) => {
      recordCursor();
      let { value } = event.target;
      if (props.formatter && props.parser) {
        value = props.parser(value);
      }
      if (isComposing.value)
        return;
      if (value === nativeInputValue.value) {
        setNativeInputValue();
        return;
      }
      emit(UPDATE_MODEL_EVENT, value);
      emit("input", value);
      await nextTick();
      setNativeInputValue();
      setCursor();
    };
    const handleChange = (event) => {
      let { value } = event.target;
      if (props.formatter && props.parser) {
        value = props.parser(value);
      }
      emit("change", value);
    };
    const {
      isComposing,
      handleCompositionStart,
      handleCompositionUpdate,
      handleCompositionEnd
    } = useComposition({ emit, afterComposition: handleInput });
    const handlePasswordVisible = () => {
      recordCursor();
      passwordVisible.value = !passwordVisible.value;
      setTimeout(setCursor);
    };
    const focus = () => {
      var _a;
      return (_a = _ref.value) == null ? void 0 : _a.focus();
    };
    const blur = () => {
      var _a;
      return (_a = _ref.value) == null ? void 0 : _a.blur();
    };
    const handleMouseLeave = (evt) => {
      hovering.value = false;
      emit("mouseleave", evt);
    };
    const handleMouseEnter = (evt) => {
      hovering.value = true;
      emit("mouseenter", evt);
    };
    const handleKeydown = (evt) => {
      emit("keydown", evt);
    };
    const select = () => {
      var _a;
      (_a = _ref.value) == null ? void 0 : _a.select();
    };
    const clear = () => {
      emit(UPDATE_MODEL_EVENT, "");
      emit("change", "");
      emit("clear");
      emit("input", "");
    };
    watch(() => props.modelValue, () => {
      var _a;
      nextTick(() => resizeTextarea());
      if (props.validateEvent) {
        (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, "change").catch((err) => debugWarn());
      }
    });
    watch(nativeInputValue, () => setNativeInputValue());
    watch(() => props.type, async () => {
      await nextTick();
      setNativeInputValue();
      resizeTextarea();
    });
    onMounted(() => {
      if (!props.formatter && props.parser)
        ;
      setNativeInputValue();
      nextTick(resizeTextarea);
    });
    expose({
      input,
      textarea,
      ref: _ref,
      textareaStyle,
      autosize: toRef(props, "autosize"),
      isComposing,
      focus,
      blur,
      select,
      clear,
      resizeTextarea
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([
          unref(containerKls),
          {
            [unref(nsInput).bm("group", "append")]: _ctx.$slots.append,
            [unref(nsInput).bm("group", "prepend")]: _ctx.$slots.prepend
          }
        ]),
        style: normalizeStyle(unref(containerStyle)),
        onMouseenter: handleMouseEnter,
        onMouseleave: handleMouseLeave
      }, [
        createCommentVNode(" input "),
        _ctx.type !== "textarea" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
          createCommentVNode(" prepend slot "),
          _ctx.$slots.prepend ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass(unref(nsInput).be("group", "prepend"))
          }, [
            renderSlot(_ctx.$slots, "prepend")
          ], 2)) : createCommentVNode("v-if", true),
          createBaseVNode("div", {
            ref_key: "wrapperRef",
            ref: wrapperRef,
            class: normalizeClass(unref(wrapperKls))
          }, [
            createCommentVNode(" prefix slot "),
            _ctx.$slots.prefix || _ctx.prefixIcon ? (openBlock(), createElementBlock("span", {
              key: 0,
              class: normalizeClass(unref(nsInput).e("prefix"))
            }, [
              createBaseVNode("span", {
                class: normalizeClass(unref(nsInput).e("prefix-inner"))
              }, [
                renderSlot(_ctx.$slots, "prefix"),
                _ctx.prefixIcon ? (openBlock(), createBlock(unref(ElIcon), {
                  key: 0,
                  class: normalizeClass(unref(nsInput).e("icon"))
                }, {
                  default: withCtx(() => [
                    (openBlock(), createBlock(resolveDynamicComponent(_ctx.prefixIcon)))
                  ]),
                  _: 1
                }, 8, ["class"])) : createCommentVNode("v-if", true)
              ], 2)
            ], 2)) : createCommentVNode("v-if", true),
            createBaseVNode("input", mergeProps({
              id: unref(inputId),
              ref_key: "input",
              ref: input,
              class: unref(nsInput).e("inner")
            }, unref(attrs), {
              minlength: _ctx.minlength,
              maxlength: _ctx.maxlength,
              type: _ctx.showPassword ? passwordVisible.value ? "text" : "password" : _ctx.type,
              disabled: unref(inputDisabled),
              readonly: _ctx.readonly,
              autocomplete: _ctx.autocomplete,
              tabindex: _ctx.tabindex,
              "aria-label": _ctx.ariaLabel,
              placeholder: _ctx.placeholder,
              style: _ctx.inputStyle,
              form: _ctx.form,
              autofocus: _ctx.autofocus,
              role: _ctx.containerRole,
              onCompositionstart: unref(handleCompositionStart),
              onCompositionupdate: unref(handleCompositionUpdate),
              onCompositionend: unref(handleCompositionEnd),
              onInput: handleInput,
              onChange: handleChange,
              onKeydown: handleKeydown
            }), null, 16, ["id", "minlength", "maxlength", "type", "disabled", "readonly", "autocomplete", "tabindex", "aria-label", "placeholder", "form", "autofocus", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend"]),
            createCommentVNode(" suffix slot "),
            unref(suffixVisible) ? (openBlock(), createElementBlock("span", {
              key: 1,
              class: normalizeClass(unref(nsInput).e("suffix"))
            }, [
              createBaseVNode("span", {
                class: normalizeClass(unref(nsInput).e("suffix-inner"))
              }, [
                !unref(showClear) || !unref(showPwdVisible) || !unref(isWordLimitVisible) ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                  renderSlot(_ctx.$slots, "suffix"),
                  _ctx.suffixIcon ? (openBlock(), createBlock(unref(ElIcon), {
                    key: 0,
                    class: normalizeClass(unref(nsInput).e("icon"))
                  }, {
                    default: withCtx(() => [
                      (openBlock(), createBlock(resolveDynamicComponent(_ctx.suffixIcon)))
                    ]),
                    _: 1
                  }, 8, ["class"])) : createCommentVNode("v-if", true)
                ], 64)) : createCommentVNode("v-if", true),
                unref(showClear) ? (openBlock(), createBlock(unref(ElIcon), {
                  key: 1,
                  class: normalizeClass([unref(nsInput).e("icon"), unref(nsInput).e("clear")]),
                  onMousedown: withModifiers(unref(NOOP), ["prevent"]),
                  onClick: clear
                }, {
                  default: withCtx(() => [
                    createVNode(unref(circle_close_default))
                  ]),
                  _: 1
                }, 8, ["class", "onMousedown"])) : createCommentVNode("v-if", true),
                unref(showPwdVisible) ? (openBlock(), createBlock(unref(ElIcon), {
                  key: 2,
                  class: normalizeClass([unref(nsInput).e("icon"), unref(nsInput).e("password")]),
                  onClick: handlePasswordVisible
                }, {
                  default: withCtx(() => [
                    (openBlock(), createBlock(resolveDynamicComponent(unref(passwordIcon))))
                  ]),
                  _: 1
                }, 8, ["class"])) : createCommentVNode("v-if", true),
                unref(isWordLimitVisible) ? (openBlock(), createElementBlock("span", {
                  key: 3,
                  class: normalizeClass(unref(nsInput).e("count"))
                }, [
                  createBaseVNode("span", {
                    class: normalizeClass(unref(nsInput).e("count-inner"))
                  }, toDisplayString(unref(textLength)) + " / " + toDisplayString(_ctx.maxlength), 3)
                ], 2)) : createCommentVNode("v-if", true),
                unref(validateState) && unref(validateIcon) && unref(needStatusIcon) ? (openBlock(), createBlock(unref(ElIcon), {
                  key: 4,
                  class: normalizeClass([
                    unref(nsInput).e("icon"),
                    unref(nsInput).e("validateIcon"),
                    unref(nsInput).is("loading", unref(validateState) === "validating")
                  ])
                }, {
                  default: withCtx(() => [
                    (openBlock(), createBlock(resolveDynamicComponent(unref(validateIcon))))
                  ]),
                  _: 1
                }, 8, ["class"])) : createCommentVNode("v-if", true)
              ], 2)
            ], 2)) : createCommentVNode("v-if", true)
          ], 2),
          createCommentVNode(" append slot "),
          _ctx.$slots.append ? (openBlock(), createElementBlock("div", {
            key: 1,
            class: normalizeClass(unref(nsInput).be("group", "append"))
          }, [
            renderSlot(_ctx.$slots, "append")
          ], 2)) : createCommentVNode("v-if", true)
        ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          createCommentVNode(" textarea "),
          createBaseVNode("textarea", mergeProps({
            id: unref(inputId),
            ref_key: "textarea",
            ref: textarea,
            class: [unref(nsTextarea).e("inner"), unref(nsInput).is("focus", unref(isFocused))]
          }, unref(attrs), {
            minlength: _ctx.minlength,
            maxlength: _ctx.maxlength,
            tabindex: _ctx.tabindex,
            disabled: unref(inputDisabled),
            readonly: _ctx.readonly,
            autocomplete: _ctx.autocomplete,
            style: unref(textareaStyle),
            "aria-label": _ctx.ariaLabel,
            placeholder: _ctx.placeholder,
            form: _ctx.form,
            autofocus: _ctx.autofocus,
            rows: _ctx.rows,
            role: _ctx.containerRole,
            onCompositionstart: unref(handleCompositionStart),
            onCompositionupdate: unref(handleCompositionUpdate),
            onCompositionend: unref(handleCompositionEnd),
            onInput: handleInput,
            onFocus: unref(handleFocus),
            onBlur: unref(handleBlur),
            onChange: handleChange,
            onKeydown: handleKeydown
          }), null, 16, ["id", "minlength", "maxlength", "tabindex", "disabled", "readonly", "autocomplete", "aria-label", "placeholder", "form", "autofocus", "rows", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend", "onFocus", "onBlur"]),
          unref(isWordLimitVisible) ? (openBlock(), createElementBlock("span", {
            key: 0,
            style: normalizeStyle(countStyle.value),
            class: normalizeClass(unref(nsInput).e("count"))
          }, toDisplayString(unref(textLength)) + " / " + toDisplayString(_ctx.maxlength), 7)) : createCommentVNode("v-if", true)
        ], 64))
      ], 38);
    };
  }
});
var Input = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["__file", "input.vue"]]);
const ElInput = withInstall(Input);
const alphaSliderProps = buildProps({
  color: {
    type: definePropType(Object),
    required: true
  },
  vertical: {
    type: Boolean,
    default: false
  }
});
let isDragging = false;
function draggable(element, options) {
  if (!isClient)
    return;
  const moveFn = function(event) {
    var _a;
    (_a = options.drag) == null ? void 0 : _a.call(options, event);
  };
  const upFn = function(event) {
    var _a;
    document.removeEventListener("mousemove", moveFn);
    document.removeEventListener("mouseup", upFn);
    document.removeEventListener("touchmove", moveFn);
    document.removeEventListener("touchend", upFn);
    document.onselectstart = null;
    document.ondragstart = null;
    isDragging = false;
    (_a = options.end) == null ? void 0 : _a.call(options, event);
  };
  const downFn = function(event) {
    var _a;
    if (isDragging)
      return;
    event.preventDefault();
    document.onselectstart = () => false;
    document.ondragstart = () => false;
    document.addEventListener("mousemove", moveFn);
    document.addEventListener("mouseup", upFn);
    document.addEventListener("touchmove", moveFn);
    document.addEventListener("touchend", upFn);
    isDragging = true;
    (_a = options.start) == null ? void 0 : _a.call(options, event);
  };
  element.addEventListener("mousedown", downFn);
  element.addEventListener("touchstart", downFn, { passive: false });
}
const useAlphaSlider = (props) => {
  const instance = getCurrentInstance();
  const { t } = useLocale();
  const thumb = shallowRef();
  const bar = shallowRef();
  const alpha = computed(() => props.color.get("alpha"));
  const alphaLabel = computed(() => t("el.colorpicker.alphaLabel"));
  function handleClick(event) {
    var _a;
    const target = event.target;
    if (target !== thumb.value) {
      handleDrag(event);
    }
    (_a = thumb.value) == null ? void 0 : _a.focus();
  }
  function handleDrag(event) {
    if (!bar.value || !thumb.value)
      return;
    const el = instance.vnode.el;
    const rect = el.getBoundingClientRect();
    const { clientX, clientY } = getClientXY(event);
    if (!props.vertical) {
      let left = clientX - rect.left;
      left = Math.max(thumb.value.offsetWidth / 2, left);
      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);
      props.color.set("alpha", Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 100));
    } else {
      let top = clientY - rect.top;
      top = Math.max(thumb.value.offsetHeight / 2, top);
      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);
      props.color.set("alpha", Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 100));
    }
  }
  function handleKeydown(event) {
    const { code, shiftKey } = event;
    const step = shiftKey ? 10 : 1;
    switch (code) {
      case EVENT_CODE.left:
      case EVENT_CODE.down:
        event.preventDefault();
        event.stopPropagation();
        incrementPosition(-step);
        break;
      case EVENT_CODE.right:
      case EVENT_CODE.up:
        event.preventDefault();
        event.stopPropagation();
        incrementPosition(step);
        break;
    }
  }
  function incrementPosition(step) {
    let next = alpha.value + step;
    next = next < 0 ? 0 : next > 100 ? 100 : next;
    props.color.set("alpha", next);
  }
  return {
    thumb,
    bar,
    alpha,
    alphaLabel,
    handleDrag,
    handleClick,
    handleKeydown
  };
};
const useAlphaSliderDOM = (props, {
  bar,
  thumb,
  handleDrag
}) => {
  const instance = getCurrentInstance();
  const ns = useNamespace("color-alpha-slider");
  const thumbLeft = ref(0);
  const thumbTop = ref(0);
  const background = ref();
  function getThumbLeft() {
    if (!thumb.value)
      return 0;
    if (props.vertical)
      return 0;
    const el = instance.vnode.el;
    const alpha = props.color.get("alpha");
    if (!el)
      return 0;
    return Math.round(alpha * (el.offsetWidth - thumb.value.offsetWidth / 2) / 100);
  }
  function getThumbTop() {
    if (!thumb.value)
      return 0;
    const el = instance.vnode.el;
    if (!props.vertical)
      return 0;
    const alpha = props.color.get("alpha");
    if (!el)
      return 0;
    return Math.round(alpha * (el.offsetHeight - thumb.value.offsetHeight / 2) / 100);
  }
  function getBackground() {
    if (props.color && props.color.value) {
      const { r, g, b } = props.color.toRgb();
      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`;
    }
    return "";
  }
  function update() {
    thumbLeft.value = getThumbLeft();
    thumbTop.value = getThumbTop();
    background.value = getBackground();
  }
  onMounted(() => {
    if (!bar.value || !thumb.value)
      return;
    const dragConfig = {
      drag: (event) => {
        handleDrag(event);
      },
      end: (event) => {
        handleDrag(event);
      }
    };
    draggable(bar.value, dragConfig);
    draggable(thumb.value, dragConfig);
    update();
  });
  watch(() => props.color.get("alpha"), () => update());
  watch(() => props.color.value, () => update());
  const rootKls = computed(() => [ns.b(), ns.is("vertical", props.vertical)]);
  const barKls = computed(() => ns.e("bar"));
  const thumbKls = computed(() => ns.e("thumb"));
  const barStyle = computed(() => ({ background: background.value }));
  const thumbStyle = computed(() => ({
    left: addUnit(thumbLeft.value),
    top: addUnit(thumbTop.value)
  }));
  return { rootKls, barKls, barStyle, thumbKls, thumbStyle, update };
};
const COMPONENT_NAME = "ElColorAlphaSlider";
const __default__$1 = defineComponent({
  name: COMPONENT_NAME
});
const _sfc_main$b = /* @__PURE__ */ defineComponent({
  ...__default__$1,
  props: alphaSliderProps,
  setup(__props, { expose }) {
    const props = __props;
    const {
      alpha,
      alphaLabel,
      bar,
      thumb,
      handleDrag,
      handleClick,
      handleKeydown
    } = useAlphaSlider(props);
    const { rootKls, barKls, barStyle, thumbKls, thumbStyle, update } = useAlphaSliderDOM(props, {
      bar,
      thumb,
      handleDrag
    });
    expose({
      update,
      bar,
      thumb
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(rootKls))
      }, [
        createBaseVNode("div", {
          ref_key: "bar",
          ref: bar,
          class: normalizeClass(unref(barKls)),
          style: normalizeStyle(unref(barStyle)),
          onClick: unref(handleClick)
        }, null, 14, ["onClick"]),
        createBaseVNode("div", {
          ref_key: "thumb",
          ref: thumb,
          class: normalizeClass(unref(thumbKls)),
          style: normalizeStyle(unref(thumbStyle)),
          "aria-label": unref(alphaLabel),
          "aria-valuenow": unref(alpha),
          "aria-orientation": _ctx.vertical ? "vertical" : "horizontal",
          "aria-valuemin": "0",
          "aria-valuemax": "100",
          role: "slider",
          tabindex: "0",
          onKeydown: unref(handleKeydown)
        }, null, 46, ["aria-label", "aria-valuenow", "aria-orientation", "onKeydown"])
      ], 2);
    };
  }
});
var AlphaSlider = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["__file", "alpha-slider.vue"]]);
const _sfc_main$a = defineComponent({
  name: "ElColorHueSlider",
  props: {
    color: {
      type: Object,
      required: true
    },
    vertical: Boolean
  },
  setup(props) {
    const ns = useNamespace("color-hue-slider");
    const instance = getCurrentInstance();
    const thumb = ref();
    const bar = ref();
    const thumbLeft = ref(0);
    const thumbTop = ref(0);
    const hueValue = computed(() => {
      return props.color.get("hue");
    });
    watch(() => hueValue.value, () => {
      update();
    });
    function handleClick(event) {
      const target = event.target;
      if (target !== thumb.value) {
        handleDrag(event);
      }
    }
    function handleDrag(event) {
      if (!bar.value || !thumb.value)
        return;
      const el = instance.vnode.el;
      const rect = el.getBoundingClientRect();
      const { clientX, clientY } = getClientXY(event);
      let hue;
      if (!props.vertical) {
        let left = clientX - rect.left;
        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);
        left = Math.max(thumb.value.offsetWidth / 2, left);
        hue = Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 360);
      } else {
        let top = clientY - rect.top;
        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);
        top = Math.max(thumb.value.offsetHeight / 2, top);
        hue = Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 360);
      }
      props.color.set("hue", hue);
    }
    function getThumbLeft() {
      if (!thumb.value)
        return 0;
      const el = instance.vnode.el;
      if (props.vertical)
        return 0;
      const hue = props.color.get("hue");
      if (!el)
        return 0;
      return Math.round(hue * (el.offsetWidth - thumb.value.offsetWidth / 2) / 360);
    }
    function getThumbTop() {
      if (!thumb.value)
        return 0;
      const el = instance.vnode.el;
      if (!props.vertical)
        return 0;
      const hue = props.color.get("hue");
      if (!el)
        return 0;
      return Math.round(hue * (el.offsetHeight - thumb.value.offsetHeight / 2) / 360);
    }
    function update() {
      thumbLeft.value = getThumbLeft();
      thumbTop.value = getThumbTop();
    }
    onMounted(() => {
      if (!bar.value || !thumb.value)
        return;
      const dragConfig = {
        drag: (event) => {
          handleDrag(event);
        },
        end: (event) => {
          handleDrag(event);
        }
      };
      draggable(bar.value, dragConfig);
      draggable(thumb.value, dragConfig);
      update();
    });
    return {
      bar,
      thumb,
      thumbLeft,
      thumbTop,
      hueValue,
      handleClick,
      update,
      ns
    };
  }
});
function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is("vertical", _ctx.vertical)])
  }, [
    createBaseVNode("div", {
      ref: "bar",
      class: normalizeClass(_ctx.ns.e("bar")),
      onClick: _ctx.handleClick
    }, null, 10, ["onClick"]),
    createBaseVNode("div", {
      ref: "thumb",
      class: normalizeClass(_ctx.ns.e("thumb")),
      style: normalizeStyle({
        left: _ctx.thumbLeft + "px",
        top: _ctx.thumbTop + "px"
      })
    }, null, 6)
  ], 2);
}
var HueSlider = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$2], ["__file", "hue-slider.vue"]]);
const colorPickerProps = buildProps({
  modelValue: String,
  id: String,
  showAlpha: Boolean,
  colorFormat: String,
  disabled: Boolean,
  size: useSizeProp,
  popperClass: {
    type: String,
    default: ""
  },
  tabindex: {
    type: [String, Number],
    default: 0
  },
  teleported: useTooltipContentProps.teleported,
  predefine: {
    type: definePropType(Array)
  },
  validateEvent: {
    type: Boolean,
    default: true
  },
  ...useAriaProps(["ariaLabel"])
});
const colorPickerEmits = {
  [UPDATE_MODEL_EVENT]: (val) => isString(val) || isNil(val),
  [CHANGE_EVENT]: (val) => isString(val) || isNil(val),
  activeChange: (val) => isString(val) || isNil(val),
  focus: (evt) => evt instanceof FocusEvent,
  blur: (evt) => evt instanceof FocusEvent
};
const colorPickerContextKey = Symbol("colorPickerContextKey");
const hsv2hsl = function(hue, sat, val) {
  return [
    hue,
    sat * val / ((hue = (2 - sat) * val) < 1 ? hue : 2 - hue) || 0,
    hue / 2
  ];
};
const isOnePointZero = function(n) {
  return isString(n) && n.includes(".") && Number.parseFloat(n) === 1;
};
const isPercentage = function(n) {
  return isString(n) && n.includes("%");
};
const bound01 = function(value, max) {
  if (isOnePointZero(value))
    value = "100%";
  const processPercent = isPercentage(value);
  value = Math.min(max, Math.max(0, Number.parseFloat(`${value}`)));
  if (processPercent) {
    value = Number.parseInt(`${value * max}`, 10) / 100;
  }
  if (Math.abs(value - max) < 1e-6) {
    return 1;
  }
  return value % max / Number.parseFloat(max);
};
const INT_HEX_MAP = {
  10: "A",
  11: "B",
  12: "C",
  13: "D",
  14: "E",
  15: "F"
};
const hexOne = (value) => {
  value = Math.min(Math.round(value), 255);
  const high = Math.floor(value / 16);
  const low = value % 16;
  return `${INT_HEX_MAP[high] || high}${INT_HEX_MAP[low] || low}`;
};
const toHex = function({ r, g, b }) {
  if (Number.isNaN(+r) || Number.isNaN(+g) || Number.isNaN(+b))
    return "";
  return `#${hexOne(r)}${hexOne(g)}${hexOne(b)}`;
};
const HEX_INT_MAP = {
  A: 10,
  B: 11,
  C: 12,
  D: 13,
  E: 14,
  F: 15
};
const parseHexChannel = function(hex) {
  if (hex.length === 2) {
    return (HEX_INT_MAP[hex[0].toUpperCase()] || +hex[0]) * 16 + (HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1]);
  }
  return HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1];
};
const hsl2hsv = function(hue, sat, light) {
  sat = sat / 100;
  light = light / 100;
  let smin = sat;
  const lmin = Math.max(light, 0.01);
  light *= 2;
  sat *= light <= 1 ? light : 2 - light;
  smin *= lmin <= 1 ? lmin : 2 - lmin;
  const v = (light + sat) / 2;
  const sv = light === 0 ? 2 * smin / (lmin + smin) : 2 * sat / (light + sat);
  return {
    h: hue,
    s: sv * 100,
    v: v * 100
  };
};
const rgb2hsv = (r, g, b) => {
  r = bound01(r, 255);
  g = bound01(g, 255);
  b = bound01(b, 255);
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h;
  const v = max;
  const d = max - min;
  const s = max === 0 ? 0 : d / max;
  if (max === min) {
    h = 0;
  } else {
    switch (max) {
      case r: {
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      }
      case g: {
        h = (b - r) / d + 2;
        break;
      }
      case b: {
        h = (r - g) / d + 4;
        break;
      }
    }
    h /= 6;
  }
  return { h: h * 360, s: s * 100, v: v * 100 };
};
const hsv2rgb = function(h, s, v) {
  h = bound01(h, 360) * 6;
  s = bound01(s, 100);
  v = bound01(v, 100);
  const i = Math.floor(h);
  const f = h - i;
  const p = v * (1 - s);
  const q = v * (1 - f * s);
  const t = v * (1 - (1 - f) * s);
  const mod = i % 6;
  const r = [v, q, p, p, t, v][mod];
  const g = [t, v, v, q, p, p][mod];
  const b = [p, p, t, v, v, q][mod];
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
};
class Color {
  constructor(options = {}) {
    this._hue = 0;
    this._saturation = 100;
    this._value = 100;
    this._alpha = 100;
    this.enableAlpha = false;
    this.format = "hex";
    this.value = "";
    for (const option in options) {
      if (hasOwn(options, option)) {
        this[option] = options[option];
      }
    }
    if (options.value) {
      this.fromString(options.value);
    } else {
      this.doOnChange();
    }
  }
  set(prop, value) {
    if (arguments.length === 1 && typeof prop === "object") {
      for (const p in prop) {
        if (hasOwn(prop, p)) {
          this.set(p, prop[p]);
        }
      }
      return;
    }
    this[`_${prop}`] = value;
    this.doOnChange();
  }
  get(prop) {
    if (prop === "alpha") {
      return Math.floor(this[`_${prop}`]);
    }
    return this[`_${prop}`];
  }
  toRgb() {
    return hsv2rgb(this._hue, this._saturation, this._value);
  }
  fromString(value) {
    if (!value) {
      this._hue = 0;
      this._saturation = 100;
      this._value = 100;
      this.doOnChange();
      return;
    }
    const fromHSV = (h, s, v) => {
      this._hue = Math.max(0, Math.min(360, h));
      this._saturation = Math.max(0, Math.min(100, s));
      this._value = Math.max(0, Math.min(100, v));
      this.doOnChange();
    };
    if (value.includes("hsl")) {
      const parts = value.replace(/hsla|hsl|\(|\)/gm, "").split(/\s|,/g).filter((val) => val !== "").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));
      if (parts.length === 4) {
        this._alpha = Number.parseFloat(parts[3]) * 100;
      } else if (parts.length === 3) {
        this._alpha = 100;
      }
      if (parts.length >= 3) {
        const { h, s, v } = hsl2hsv(parts[0], parts[1], parts[2]);
        fromHSV(h, s, v);
      }
    } else if (value.includes("hsv")) {
      const parts = value.replace(/hsva|hsv|\(|\)/gm, "").split(/\s|,/g).filter((val) => val !== "").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));
      if (parts.length === 4) {
        this._alpha = Number.parseFloat(parts[3]) * 100;
      } else if (parts.length === 3) {
        this._alpha = 100;
      }
      if (parts.length >= 3) {
        fromHSV(parts[0], parts[1], parts[2]);
      }
    } else if (value.includes("rgb")) {
      const parts = value.replace(/rgba|rgb|\(|\)/gm, "").split(/\s|,/g).filter((val) => val !== "").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));
      if (parts.length === 4) {
        this._alpha = Number.parseFloat(parts[3]) * 100;
      } else if (parts.length === 3) {
        this._alpha = 100;
      }
      if (parts.length >= 3) {
        const { h, s, v } = rgb2hsv(parts[0], parts[1], parts[2]);
        fromHSV(h, s, v);
      }
    } else if (value.includes("#")) {
      const hex = value.replace("#", "").trim();
      if (!/^[0-9a-fA-F]{3}$|^[0-9a-fA-F]{6}$|^[0-9a-fA-F]{8}$/.test(hex))
        return;
      let r, g, b;
      if (hex.length === 3) {
        r = parseHexChannel(hex[0] + hex[0]);
        g = parseHexChannel(hex[1] + hex[1]);
        b = parseHexChannel(hex[2] + hex[2]);
      } else if (hex.length === 6 || hex.length === 8) {
        r = parseHexChannel(hex.slice(0, 2));
        g = parseHexChannel(hex.slice(2, 4));
        b = parseHexChannel(hex.slice(4, 6));
      }
      if (hex.length === 8) {
        this._alpha = parseHexChannel(hex.slice(6)) / 255 * 100;
      } else if (hex.length === 3 || hex.length === 6) {
        this._alpha = 100;
      }
      const { h, s, v } = rgb2hsv(r, g, b);
      fromHSV(h, s, v);
    }
  }
  compare(color) {
    return Math.abs(color._hue - this._hue) < 2 && Math.abs(color._saturation - this._saturation) < 1 && Math.abs(color._value - this._value) < 1 && Math.abs(color._alpha - this._alpha) < 1;
  }
  doOnChange() {
    const { _hue, _saturation, _value, _alpha, format } = this;
    if (this.enableAlpha) {
      switch (format) {
        case "hsl": {
          const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100);
          this.value = `hsla(${_hue}, ${Math.round(hsl[1] * 100)}%, ${Math.round(hsl[2] * 100)}%, ${this.get("alpha") / 100})`;
          break;
        }
        case "hsv": {
          this.value = `hsva(${_hue}, ${Math.round(_saturation)}%, ${Math.round(_value)}%, ${this.get("alpha") / 100})`;
          break;
        }
        case "hex": {
          this.value = `${toHex(hsv2rgb(_hue, _saturation, _value))}${hexOne(_alpha * 255 / 100)}`;
          break;
        }
        default: {
          const { r, g, b } = hsv2rgb(_hue, _saturation, _value);
          this.value = `rgba(${r}, ${g}, ${b}, ${this.get("alpha") / 100})`;
        }
      }
    } else {
      switch (format) {
        case "hsl": {
          const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100);
          this.value = `hsl(${_hue}, ${Math.round(hsl[1] * 100)}%, ${Math.round(hsl[2] * 100)}%)`;
          break;
        }
        case "hsv": {
          this.value = `hsv(${_hue}, ${Math.round(_saturation)}%, ${Math.round(_value)}%)`;
          break;
        }
        case "rgb": {
          const { r, g, b } = hsv2rgb(_hue, _saturation, _value);
          this.value = `rgb(${r}, ${g}, ${b})`;
          break;
        }
        default: {
          this.value = toHex(hsv2rgb(_hue, _saturation, _value));
        }
      }
    }
  }
}
const _sfc_main$9 = defineComponent({
  props: {
    colors: {
      type: Array,
      required: true
    },
    color: {
      type: Object,
      required: true
    },
    enableAlpha: {
      type: Boolean,
      required: true
    }
  },
  setup(props) {
    const ns = useNamespace("color-predefine");
    const { currentColor } = inject(colorPickerContextKey);
    const rgbaColors = ref(parseColors(props.colors, props.color));
    watch(() => currentColor.value, (val) => {
      const color = new Color();
      color.fromString(val);
      rgbaColors.value.forEach((item) => {
        item.selected = color.compare(item);
      });
    });
    watchEffect(() => {
      rgbaColors.value = parseColors(props.colors, props.color);
    });
    function handleSelect(index) {
      props.color.fromString(props.colors[index]);
    }
    function parseColors(colors, color) {
      return colors.map((value) => {
        const c = new Color();
        c.enableAlpha = props.enableAlpha;
        c.format = "rgba";
        c.fromString(value);
        c.selected = c.value === color.value;
        return c;
      });
    }
    return {
      rgbaColors,
      handleSelect,
      ns
    };
  }
});
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.ns.b())
  }, [
    createBaseVNode("div", {
      class: normalizeClass(_ctx.ns.e("colors"))
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rgbaColors, (item, index) => {
        return openBlock(), createElementBlock("div", {
          key: _ctx.colors[index],
          class: normalizeClass([
            _ctx.ns.e("color-selector"),
            _ctx.ns.is("alpha", item._alpha < 100),
            { selected: item.selected }
          ]),
          onClick: ($event) => _ctx.handleSelect(index)
        }, [
          createBaseVNode("div", {
            style: normalizeStyle({ backgroundColor: item.value })
          }, null, 4)
        ], 10, ["onClick"]);
      }), 128))
    ], 2)
  ], 2);
}
var Predefine = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$1], ["__file", "predefine.vue"]]);
const _sfc_main$8 = defineComponent({
  name: "ElSlPanel",
  props: {
    color: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const ns = useNamespace("color-svpanel");
    const instance = getCurrentInstance();
    const cursorTop = ref(0);
    const cursorLeft = ref(0);
    const background = ref("hsl(0, 100%, 50%)");
    const colorValue = computed(() => {
      const hue = props.color.get("hue");
      const value = props.color.get("value");
      return { hue, value };
    });
    function update() {
      const saturation = props.color.get("saturation");
      const value = props.color.get("value");
      const el = instance.vnode.el;
      const { clientWidth: width, clientHeight: height } = el;
      cursorLeft.value = saturation * width / 100;
      cursorTop.value = (100 - value) * height / 100;
      background.value = `hsl(${props.color.get("hue")}, 100%, 50%)`;
    }
    function handleDrag(event) {
      const el = instance.vnode.el;
      const rect = el.getBoundingClientRect();
      const { clientX, clientY } = getClientXY(event);
      let left = clientX - rect.left;
      let top = clientY - rect.top;
      left = Math.max(0, left);
      left = Math.min(left, rect.width);
      top = Math.max(0, top);
      top = Math.min(top, rect.height);
      cursorLeft.value = left;
      cursorTop.value = top;
      props.color.set({
        saturation: left / rect.width * 100,
        value: 100 - top / rect.height * 100
      });
    }
    watch(() => colorValue.value, () => {
      update();
    });
    onMounted(() => {
      draggable(instance.vnode.el, {
        drag: (event) => {
          handleDrag(event);
        },
        end: (event) => {
          handleDrag(event);
        }
      });
      update();
    });
    return {
      cursorTop,
      cursorLeft,
      background,
      colorValue,
      handleDrag,
      update,
      ns
    };
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.ns.b()),
    style: normalizeStyle({
      backgroundColor: _ctx.background
    })
  }, [
    createBaseVNode("div", {
      class: normalizeClass(_ctx.ns.e("white"))
    }, null, 2),
    createBaseVNode("div", {
      class: normalizeClass(_ctx.ns.e("black"))
    }, null, 2),
    createBaseVNode("div", {
      class: normalizeClass(_ctx.ns.e("cursor")),
      style: normalizeStyle({
        top: _ctx.cursorTop + "px",
        left: _ctx.cursorLeft + "px"
      })
    }, [
      createBaseVNode("div")
    ], 6)
  ], 6);
}
var SvPanel = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render], ["__file", "sv-panel.vue"]]);
const __default__ = defineComponent({
  name: "ElColorPicker"
});
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  ...__default__,
  props: colorPickerProps,
  emits: colorPickerEmits,
  setup(__props, { expose, emit }) {
    const props = __props;
    const { t } = useLocale();
    const ns = useNamespace("color");
    const { formItem } = useFormItem();
    const colorSize = useFormSize();
    const colorDisabled = useFormDisabled();
    const { inputId: buttonId, isLabeledByFormItem } = useFormItemInputId(props, {
      formItemContext: formItem
    });
    const hue = ref();
    const sv = ref();
    const alpha = ref();
    const popper = ref();
    const triggerRef = ref();
    const inputRef = ref();
    const { isFocused, handleFocus, handleBlur } = useFocusController(triggerRef, {
      beforeFocus() {
        return colorDisabled.value;
      },
      beforeBlur(event) {
        var _a;
        return (_a = popper.value) == null ? void 0 : _a.isFocusInsideContent(event);
      },
      afterBlur() {
        setShowPicker(false);
        resetColor();
      }
    });
    let shouldActiveChange = true;
    const color = reactive(new Color({
      enableAlpha: props.showAlpha,
      format: props.colorFormat || "",
      value: props.modelValue
    }));
    const showPicker = ref(false);
    const showPanelColor = ref(false);
    const customInput = ref("");
    const displayedColor = computed(() => {
      if (!props.modelValue && !showPanelColor.value) {
        return "transparent";
      }
      return displayedRgb(color, props.showAlpha);
    });
    const currentColor = computed(() => {
      return !props.modelValue && !showPanelColor.value ? "" : color.value;
    });
    const buttonAriaLabel = computed(() => {
      return !isLabeledByFormItem.value ? props.ariaLabel || t("el.colorpicker.defaultLabel") : void 0;
    });
    const buttonAriaLabelledby = computed(() => {
      return isLabeledByFormItem.value ? formItem == null ? void 0 : formItem.labelId : void 0;
    });
    const btnKls = computed(() => {
      return [
        ns.b("picker"),
        ns.is("disabled", colorDisabled.value),
        ns.bm("picker", colorSize.value),
        ns.is("focused", isFocused.value)
      ];
    });
    function displayedRgb(color2, showAlpha) {
      if (!(color2 instanceof Color)) {
        throw new TypeError("color should be instance of _color Class");
      }
      const { r, g, b } = color2.toRgb();
      return showAlpha ? `rgba(${r}, ${g}, ${b}, ${color2.get("alpha") / 100})` : `rgb(${r}, ${g}, ${b})`;
    }
    function setShowPicker(value) {
      showPicker.value = value;
    }
    const debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true });
    function show() {
      if (colorDisabled.value)
        return;
      setShowPicker(true);
    }
    function hide() {
      debounceSetShowPicker(false);
      resetColor();
    }
    function resetColor() {
      nextTick(() => {
        if (props.modelValue) {
          color.fromString(props.modelValue);
        } else {
          color.value = "";
          nextTick(() => {
            showPanelColor.value = false;
          });
        }
      });
    }
    function handleTrigger() {
      if (colorDisabled.value)
        return;
      if (showPicker.value) {
        resetColor();
      }
      debounceSetShowPicker(!showPicker.value);
    }
    function handleConfirm() {
      color.fromString(customInput.value);
    }
    function confirmValue() {
      const value = color.value;
      emit(UPDATE_MODEL_EVENT, value);
      emit("change", value);
      if (props.validateEvent) {
        formItem == null ? void 0 : formItem.validate("change").catch((err) => debugWarn());
      }
      debounceSetShowPicker(false);
      nextTick(() => {
        const newColor = new Color({
          enableAlpha: props.showAlpha,
          format: props.colorFormat || "",
          value: props.modelValue
        });
        if (!color.compare(newColor)) {
          resetColor();
        }
      });
    }
    function clear() {
      debounceSetShowPicker(false);
      emit(UPDATE_MODEL_EVENT, null);
      emit("change", null);
      if (props.modelValue !== null && props.validateEvent) {
        formItem == null ? void 0 : formItem.validate("change").catch((err) => debugWarn());
      }
      resetColor();
    }
    function handleClickOutside() {
      if (!showPicker.value)
        return;
      hide();
      isFocused.value && focus();
    }
    function handleEsc(event) {
      event.preventDefault();
      event.stopPropagation();
      setShowPicker(false);
      resetColor();
    }
    function handleKeyDown(event) {
      switch (event.code) {
        case EVENT_CODE.enter:
        case EVENT_CODE.numpadEnter:
        case EVENT_CODE.space:
          event.preventDefault();
          event.stopPropagation();
          show();
          inputRef.value.focus();
          break;
        case EVENT_CODE.esc:
          handleEsc(event);
          break;
      }
    }
    function focus() {
      triggerRef.value.focus();
    }
    function blur() {
      triggerRef.value.blur();
    }
    onMounted(() => {
      if (props.modelValue) {
        customInput.value = currentColor.value;
      }
    });
    watch(() => props.modelValue, (newVal) => {
      if (!newVal) {
        showPanelColor.value = false;
      } else if (newVal && newVal !== color.value) {
        shouldActiveChange = false;
        color.fromString(newVal);
      }
    });
    watch(() => [props.colorFormat, props.showAlpha], () => {
      color.enableAlpha = props.showAlpha;
      color.format = props.colorFormat || color.format;
      color.doOnChange();
      emit(UPDATE_MODEL_EVENT, color.value);
    });
    watch(() => currentColor.value, (val) => {
      customInput.value = val;
      shouldActiveChange && emit("activeChange", val);
      shouldActiveChange = true;
    });
    watch(() => color.value, () => {
      if (!props.modelValue && !showPanelColor.value) {
        showPanelColor.value = true;
      }
    });
    watch(() => showPicker.value, () => {
      nextTick(() => {
        var _a, _b, _c;
        (_a = hue.value) == null ? void 0 : _a.update();
        (_b = sv.value) == null ? void 0 : _b.update();
        (_c = alpha.value) == null ? void 0 : _c.update();
      });
    });
    provide(colorPickerContextKey, {
      currentColor
    });
    expose({
      color,
      show,
      hide,
      focus,
      blur
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTooltip), {
        ref_key: "popper",
        ref: popper,
        visible: showPicker.value,
        "show-arrow": false,
        "fallback-placements": ["bottom", "top", "right", "left"],
        offset: 0,
        "gpu-acceleration": false,
        "popper-class": [unref(ns).be("picker", "panel"), unref(ns).b("dropdown"), _ctx.popperClass],
        "stop-popper-mouse-event": false,
        effect: "light",
        trigger: "click",
        teleported: _ctx.teleported,
        transition: `${unref(ns).namespace.value}-zoom-in-top`,
        persistent: "",
        onHide: ($event) => setShowPicker(false)
      }, {
        content: withCtx(() => [
          withDirectives((openBlock(), createElementBlock("div", {
            onKeydown: withKeys(handleEsc, ["esc"])
          }, [
            createBaseVNode("div", {
              class: normalizeClass(unref(ns).be("dropdown", "main-wrapper"))
            }, [
              createVNode(HueSlider, {
                ref_key: "hue",
                ref: hue,
                class: "hue-slider",
                color: unref(color),
                vertical: ""
              }, null, 8, ["color"]),
              createVNode(SvPanel, {
                ref_key: "sv",
                ref: sv,
                color: unref(color)
              }, null, 8, ["color"])
            ], 2),
            _ctx.showAlpha ? (openBlock(), createBlock(AlphaSlider, {
              key: 0,
              ref_key: "alpha",
              ref: alpha,
              color: unref(color)
            }, null, 8, ["color"])) : createCommentVNode("v-if", true),
            _ctx.predefine ? (openBlock(), createBlock(Predefine, {
              key: 1,
              ref: "predefine",
              "enable-alpha": _ctx.showAlpha,
              color: unref(color),
              colors: _ctx.predefine
            }, null, 8, ["enable-alpha", "color", "colors"])) : createCommentVNode("v-if", true),
            createBaseVNode("div", {
              class: normalizeClass(unref(ns).be("dropdown", "btns"))
            }, [
              createBaseVNode("span", {
                class: normalizeClass(unref(ns).be("dropdown", "value"))
              }, [
                createVNode(unref(ElInput), {
                  ref_key: "inputRef",
                  ref: inputRef,
                  modelValue: customInput.value,
                  "onUpdate:modelValue": ($event) => customInput.value = $event,
                  "validate-event": false,
                  size: "small",
                  onKeyup: withKeys(handleConfirm, ["enter"]),
                  onBlur: handleConfirm
                }, null, 8, ["modelValue", "onUpdate:modelValue", "onKeyup"])
              ], 2),
              createVNode(unref(ElButton), {
                class: normalizeClass(unref(ns).be("dropdown", "link-btn")),
                text: "",
                size: "small",
                onClick: clear
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(t)("el.colorpicker.clear")), 1)
                ]),
                _: 1
              }, 8, ["class"]),
              createVNode(unref(ElButton), {
                plain: "",
                size: "small",
                class: normalizeClass(unref(ns).be("dropdown", "btn")),
                onClick: confirmValue
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(t)("el.colorpicker.confirm")), 1)
                ]),
                _: 1
              }, 8, ["class"])
            ], 2)
          ], 40, ["onKeydown"])), [
            [unref(ClickOutside), handleClickOutside, triggerRef.value]
          ])
        ]),
        default: withCtx(() => [
          createBaseVNode("div", mergeProps({
            id: unref(buttonId),
            ref_key: "triggerRef",
            ref: triggerRef
          }, _ctx.$attrs, {
            class: unref(btnKls),
            role: "button",
            "aria-label": unref(buttonAriaLabel),
            "aria-labelledby": unref(buttonAriaLabelledby),
            "aria-description": unref(t)("el.colorpicker.description", { color: _ctx.modelValue || "" }),
            "aria-disabled": unref(colorDisabled),
            tabindex: unref(colorDisabled) ? -1 : _ctx.tabindex,
            onKeydown: handleKeyDown,
            onFocus: unref(handleFocus),
            onBlur: unref(handleBlur)
          }), [
            unref(colorDisabled) ? (openBlock(), createElementBlock("div", {
              key: 0,
              class: normalizeClass(unref(ns).be("picker", "mask"))
            }, null, 2)) : createCommentVNode("v-if", true),
            createBaseVNode("div", {
              class: normalizeClass(unref(ns).be("picker", "trigger")),
              onClick: handleTrigger
            }, [
              createBaseVNode("span", {
                class: normalizeClass([unref(ns).be("picker", "color"), unref(ns).is("alpha", _ctx.showAlpha)])
              }, [
                createBaseVNode("span", {
                  class: normalizeClass(unref(ns).be("picker", "color-inner")),
                  style: normalizeStyle({
                    backgroundColor: unref(displayedColor)
                  })
                }, [
                  withDirectives(createVNode(unref(ElIcon), {
                    class: normalizeClass([unref(ns).be("picker", "icon"), unref(ns).is("icon-arrow-down")])
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(arrow_down_default))
                    ]),
                    _: 1
                  }, 8, ["class"]), [
                    [vShow, _ctx.modelValue || showPanelColor.value]
                  ]),
                  withDirectives(createVNode(unref(ElIcon), {
                    class: normalizeClass([unref(ns).be("picker", "empty"), unref(ns).is("icon-close")])
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(close_default))
                    ]),
                    _: 1
                  }, 8, ["class"]), [
                    [vShow, !_ctx.modelValue && !showPanelColor.value]
                  ])
                ], 6)
              ], 2)
            ], 2)
          ], 16, ["id", "aria-label", "aria-labelledby", "aria-description", "aria-disabled", "tabindex", "onFocus", "onBlur"])
        ]),
        _: 1
      }, 8, ["visible", "popper-class", "teleported", "transition", "onHide"]);
    };
  }
});
var ColorPicker = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["__file", "color-picker.vue"]]);
const ElColorPicker = withInstall(ColorPicker);
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    originWidth: { default: 1920 },
    originHeight: { default: 1080 },
    theme: { default: WhiteboardThemeEnum.GREEN }
  },
  emits: ["history-list-change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const whiteboardStore = useWhiteboardStore();
    const canvasRef = ref();
    const whiteboardCursorRef = ref();
    const { scale } = storeToRefs(useThemeStore());
    const isShowEraser = ref(false);
    const cursorPosition = ref({
      x: 0,
      y: 0
    });
    const updateEraserPosition = (position) => {
      if (!whiteboardCursorRef.value) {
        return;
      }
      cursorPosition.value = {
        x: position.x,
        y: position.y
      };
    };
    const onPointerdownWhiteboard = (eventInfo) => {
      const { hlWhiteboard, canvas } = whiteboardStore;
      if (!hlWhiteboard || !canvas) {
        return;
      }
      if (!canvas._isMainEvent(eventInfo.e)) {
        return;
      }
      isShowEraser.value = true;
      cursorPosition.value = eventInfo.viewportPoint;
      updateEraserPosition(cursorPosition.value);
    };
    const onPointermoveWhiteboard = (eventInfo) => {
      const { hlWhiteboard, canvas } = whiteboardStore;
      if (!hlWhiteboard || !canvas) {
        return;
      }
      if (!canvas._isMainEvent(eventInfo.e)) {
        return;
      }
      cursorPosition.value = eventInfo.viewportPoint;
      updateEraserPosition(cursorPosition.value);
    };
    const onPointerupWhiteboard = (eventInfo) => {
      const { hlWhiteboard, canvas } = whiteboardStore;
      if (!hlWhiteboard || !canvas) {
        return;
      }
      if (!canvas._isMainEvent(eventInfo.e)) {
        return;
      }
      isShowEraser.value = false;
    };
    watch(
      () => [props.originWidth, props.originHeight],
      () => {
        whiteboardStore.setCanvasSize(props.originWidth, props.originHeight);
      }
    );
    onMounted(() => {
      if (!canvasRef.value) {
        return;
      }
      const canvasWidth = props.originWidth;
      const canvasHeight = props.originHeight;
      const { canvas, hlWhiteboard } = whiteboardStore.initWhiteboard(
        canvasRef.value,
        {
          width: canvasWidth,
          height: canvasHeight,
          enablePointerEvents: true,
          enableRetinaScaling: false
        },
        {
          brushWidth: scale.value * DEFAULT_BRUSH_SIZE,
          brushColor: whiteboardThemeMap[props.theme].brushColor,
          maxStep: 10
        }
      );
      whiteboardStore.setCanvasSize(canvasWidth, canvasHeight);
      canvas.on("mouse:down", onPointerdownWhiteboard);
      canvas.on("mouse:move", onPointermoveWhiteboard);
      canvas.on("mouse:up", onPointerupWhiteboard);
      hlWhiteboard.on(IHistoryPluginEvents.HISTORY_LIST_CHANGE, (history, type) => {
        emits("history-list-change", history, type);
      });
    });
    onBeforeUnmount(() => {
      whiteboardStore.destroy();
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["whiteboard-wrap", `whiteboard-${_ctx.theme}`]),
        onContextmenu: _cache[0] || (_cache[0] = withModifiers(() => {
        }, ["prevent"])),
        onTouchend: _cache[1] || (_cache[1] = withModifiers(() => {
        }, ["prevent"]))
      }, [
        renderSlot(_ctx.$slots, "default", {}, void 0, true)
      ], 34);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_4bcd8f97_lang = "";
const Whiteboard = /* @__PURE__ */ _export_sfc$1(_sfc_main$6, [["__scopeId", "data-v-4bcd8f97"]]);
const _hoisted_1$4 = {
  id: "V1.2.0",
  fill: "#000000",
  transform: "translate(0, 18.8683)"
};
const _hoisted_2$4 = ["fill"];
const _hoisted_3$3 = {
  id: "V1.2.0",
  stroke: "none",
  "stroke-width": "1",
  fill: "none"
};
const _hoisted_4$2 = ["fill"];
const _hoisted_5$2 = {
  id: "白板备份",
  fill: "#000000"
};
const _hoisted_6$2 = {
  id: "V1.2.0",
  transform: "translate(28, 10.5)"
};
const _hoisted_7$2 = ["fill"];
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "WhiteboardTooldicsPencilIcon",
  setup(__props) {
    const whiteboardStore = useWhiteboardStore();
    const { brushType, brushColor, isPencilTool } = storeToRefs(whiteboardStore);
    return (_ctx, _cache) => {
      return !unref(isPencilTool) ? (openBlock(), createElementBlock("svg", {
        key: 0,
        viewBox: "0 0 1024 1024",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        class: normalizeClass(_ctx.$attrs.class)
      }, _cache[0] || (_cache[0] = [
        createBaseVNode("path", { d: "M483.27338667 604.51498667a118.82300907 118.82300907 0 0 0 79.89150506 32.25307413c5.69539093 0.46811413 11.3127616-1.62279573 15.35414827-5.7265984L911.87882667 306.9659424a127.46752 127.46752 0 0 0 36.9498208-94.2158016 127.18665173 127.18665173 0 0 0-43.0977216-91.48513493 122.6459424 122.6459424 0 0 0-92.3277408-30.03733334 123.39492587 123.39492587 0 0 0-85.3528384 46.6865984l-274.47100907 381.80961494c-3.4328384 4.80597333-4.72795413 10.82904427-3.5888768 16.64926506a126.6561216 126.6561216 0 0 0 33.28292587 68.14183574zM102.69647253 787.0171424a57.2503776 57.2503776 0 0 0 61.40098987-21.51765333c22.50069333-32.79920747 29.16352-102.50142507 77.75378347-145.55233494a170.70567573 170.70567573 0 0 1 134.0367232-44.5800832 13.82497493 13.82497493 0 0 1 11.7808768 10.76662827 139.46684907 139.46684907 0 0 0 47.06108906 86.6011424 291.32312427 291.32312427 0 0 0 97.21173334 39.4776384 12.79512427 12.79512427 0 0 1 10.7354208 12.79512427c3.07395093 45.09500907 0 215.25455253-224.070704 208.0768-256.83870507-4.1038016-238.42620907-154.2592608-215.90991254-146.0516576z" }, null, -1),
        createBaseVNode("path", { d: "M160.4773792 482.3371584a5.19606827 5.19606827 0 0 1 0-10.32972267 61.32297173 61.32297173 0 0 0 54.61333333-54.61333333 5.19606827 5.19606827 0 0 1 10.3297216 0 61.32297173 61.32297173 0 0 0 54.61333334 54.61333333 5.19606827 5.19606827 0 0 1 0 10.32972267 61.32297173 61.32297173 0 0 0-54.61333334 54.61333333 5.19606827 5.19606827 0 0 1-10.3297216 0 61.32297173 61.32297173 0 0 0-54.61333333-54.61333333z" }, null, -1),
        createBaseVNode("path", { d: "M699.24571413 886.49142827m-46.81142826 0a46.81142827 46.81142827 0 1 0 93.62285653 0 46.81142827 46.81142827 0 1 0-93.62285653 0Z" }, null, -1),
        createBaseVNode("path", { d: "M762.16027413 688.29184l-35.35823253-5.92944747a15.3229408 15.3229408 0 0 1 0-30.2245792l35.35823253-5.92944746a78.01904747 78.01904747 0 0 0 64.0536384-64.0536384l5.92944747-35.34262827a15.3229408 15.3229408 0 0 1 30.2245792 0l5.92944747 35.34262827a78.01904747 78.01904747 0 0 0 64.0536384 64.0536384l35.34262826 5.92944746a15.3229408 15.3229408 0 0 1 0 30.2401824l-35.34262826 5.92944854a78.01904747 78.01904747 0 0 0-64.0536384 64.03803413l-5.92944747 35.35823253a15.3229408 15.3229408 0 0 1-30.24018347 0l-5.92944746-35.35823253a78.01904747 78.01904747 0 0 0-64.03803414-64.0536384z" }, null, -1)
      ]), 2)) : unref(brushType) === unref(IPencilModes).SOLID ? (openBlock(), createElementBlock("svg", {
        key: 1,
        viewBox: "0 0 70 70",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        class: normalizeClass(_ctx.$attrs.class)
      }, [
        _cache[6] || (_cache[6] = createBaseVNode("title", null, "形状", -1)),
        createBaseVNode("g", _hoisted_1$4, [
          _cache[2] || (_cache[2] = createBaseVNode("path", {
            id: "星形备份-8",
            d: "M7.4246212,12.8423049 C7.28191292,12.9580306 7.07241075,12.9361569 6.956685,12.7934487 C6.85764069,12.6713112 6.85764069,12.4965061 6.956685,12.3743687 C8.12649589,10.9318055 8.12649589,8.8671844 6.956685,7.4246212 C6.84095926,7.28191292 6.86283293,7.07241075 7.00554121,6.956685 C7.12767863,6.85764069 7.30248378,6.85764069 7.4246212,6.956685 C8.8671844,8.12649589 10.9318055,8.12649589 12.3743687,6.956685 C12.517077,6.84095926 12.7265791,6.86283293 12.8423049,7.00554121 C12.9413492,7.12767863 12.9413492,7.30248378 12.8423049,7.4246212 C11.672494,8.8671844 11.672494,10.9318055 12.8423049,12.3743687 C12.9580306,12.517077 12.9361569,12.7265791 12.7934487,12.8423049 C12.6713112,12.9413492 12.4965061,12.9413492 12.3743687,12.8423049 C10.9318055,11.672494 8.8671844,11.672494 7.4246212,12.8423049 Z",
            transform: "translate(9.8995, 9.8995) rotate(45) translate(-9.8995, -9.8995)"
          }, null, -1)),
          _cache[3] || (_cache[3] = createBaseVNode("circle", {
            id: "椭圆形备份-14",
            cx: "60.5961941",
            cy: "41.131728",
            r: "3"
          }, null, -1)),
          _cache[4] || (_cache[4] = createBaseVNode("path", {
            id: "星形备份-9",
            d: "M62.1790153,27.891264 L60.3085047,29.2247552 C59.866681,29.5397323 59.2531728,29.4369029 58.9381957,28.9950792 C58.6948913,28.653792 58.6948913,28.1957334 58.9381957,27.8544462 L60.2716869,25.9839356 C61.5099141,24.2470536 61.5099141,21.9158973 60.2716869,20.1790153 L58.9381957,18.3085047 C58.6232186,17.866681 58.726048,17.2531728 59.1678717,16.9381957 C59.5091589,16.6948913 59.9672175,16.6948913 60.3085047,16.9381957 L62.1790153,18.2716869 C63.9158973,19.5099141 66.2470536,19.5099141 67.9839356,18.2716869 L69.8544462,16.9381957 C70.2962699,16.6232186 70.9097781,16.726048 71.2247552,17.1678717 C71.4680597,17.5091589 71.4680597,17.9672175 71.2247552,18.3085047 L69.891264,20.1790153 C68.6530368,21.9158973 68.6530368,24.2470536 69.891264,25.9839356 L71.2247552,27.8544462 C71.5397323,28.2962699 71.4369029,28.9097781 70.9950792,29.2247552 C70.653792,29.4680597 70.1957334,29.4680597 69.8544462,29.2247552 L67.9839356,27.891264 C66.2470536,26.6530368 63.9158973,26.6530368 62.1790153,27.891264 Z",
            transform: "translate(65.0815, 23.0815) rotate(45) translate(-65.0815, -23.0815)"
          }, null, -1)),
          _cache[5] || (_cache[5] = createBaseVNode("path", {
            id: "形状结合备份-5",
            transform: "translate(15.5962, 2.3597)",
            d: "M31.9498941,13.2900889 C32.6263937,13.2900889 33.2043182,13.7778795 33.3179367,14.4447697 L37.9979136,41.9141575 C38.126637,42.6697066 37.6184943,43.386551 36.8629453,43.5152744 C36.785947,43.5283926 36.7079787,43.5349869 36.629871,43.5349869 L1.3877551,43.5349869 C0.621319123,43.5349869 -1.31006317e-14,42.9136677 -1.28785871e-14,42.1472318 C-1.28785871e-14,42.0722433 0.00607807166,41.9973783 0.0181745144,41.9233719 L4.50808433,14.4539841 C4.61778082,13.7828581 5.19763291,13.2900889 5.87766491,13.2900889 L31.9498941,13.2900889 Z M29.6477322,16.5349869 L8.21308699,16.5349869 C7.86715016,16.5349869 7.57405748,16.7897976 7.52595123,17.1323733 L6.68370439,23.1302066 C6.67921532,23.1621744 6.67696257,23.1944164 6.67696257,23.2266978 C6.67696257,23.6099158 6.98762214,23.9205754 7.37084015,23.9205754 L30.6227894,23.9205754 C30.6600843,23.9205754 30.6973185,23.9175686 30.7341301,23.9115842 C31.1123824,23.8500924 31.3691675,23.4936093 31.3076757,23.115357 L30.3326185,17.1175237 C30.2780227,16.7816906 29.987974,16.5349869 29.6477322,16.5349869 Z M23.1404941,0 C23.7641556,0 24.1791863,0.609367376 24.2589969,1.0044386 L27.1735727,11.819546 L10.4133369,11.819546 L12.6379541,1.0044386 C12.7177647,0.609367376 13.1025844,0 13.7320289,0 L23.1404941,0 Z"
          }, null, -1)),
          createBaseVNode("g", {
            id: "V1.2.0",
            fill: unref(brushColor),
            transform: "translate(30, -12)"
          }, _cache[1] || (_cache[1] = [
            createBaseVNode("path", {
              id: "路径",
              d: "M8,13.2279704 L0,13.2279704 L3.04280554,3.16543501 C3.20266193,2.6367911 3.7608017,2.33782975 4.2894456,2.49768614 C4.60972654,2.59453575 4.86034486,2.84515407 4.95719446,3.16543501 L8,13.2279704 L8,13.2279704 Z"
            }, null, -1)
          ]), 8, _hoisted_2$4)
        ])
      ], 2)) : unref(brushType) === unref(IPencilModes).DASHED ? (openBlock(), createElementBlock("svg", {
        key: 2,
        viewBox: "0 0 70 70",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        class: normalizeClass(_ctx.$attrs.class)
      }, [
        _cache[9] || (_cache[9] = createBaseVNode("title", null, "形状", -1)),
        createBaseVNode("g", _hoisted_3$3, [
          _cache[8] || (_cache[8] = createStaticVNode('<g id="白板备份" transform="translate(11.4038, 2)" fill="#000000" data-v-23724bc4><path id="形状备份-6" d="M26.754968,32.9288132 C28.1510788,34.2372599 29.9750368,34.9736429 31.8744415,34.9956884 C32.239716,35.0263629 32.5997793,34.8922495 32.8589557,34.6289847 L54.2229128,13.8602222 C55.8076511,12.2716395 56.666482,10.0824061 56.5916866,7.82201509 C56.5168912,5.56162409 55.5152808,3.43591657 53.8291071,1.9590212 C52.1959478,0.527789513 50.0599663,-0.167091181 47.9121652,0.0341127519 C45.764364,0.235316685 43.7895076,1.31529669 42.4415601,3.02579557 L24.8515739,27.4949315 C24.632418,27.8026548 24.5491927,28.1891367 24.621854,28.5617057 C24.8725348,30.2060373 25.6176011,31.7314057 26.754968,32.9288132 L26.754968,32.9288132 Z M2.36550827,44.6254266 C3.83569263,45.0280816 5.40135389,44.4791575 6.29998404,43.2459912 C7.74262513,41.1439943 8.16886003,36.677251 11.2836534,33.9183801 C13.6017644,31.7695993 16.7331355,30.7280041 19.8739254,31.0609781 C20.2456757,31.1078551 20.547704,31.3840944 20.6280333,31.7506958 C20.9247926,33.9072503 21.9974402,35.881047 23.6444647,37.3012813 C25.5513472,38.5144869 27.6620077,39.3713313 29.8740514,39.8302463 C30.2749235,39.8953001 30.567811,40.2445755 30.5625847,40.6513388 C30.7593084,43.5415845 30.5625847,54.4456932 16.2017481,53.9858814 C-0.257475545,53.7231318 0.922867185,44.0999274 2.36550827,44.6254266 Z" fill-rule="nonzero" data-v-23724bc4></path><path id="星形备份-8" d="M7.4246212,27.7105769 C7.28191292,27.8263026 7.07241075,27.804429 6.956685,27.6617207 C6.85764069,27.5395833 6.85764069,27.3647781 6.956685,27.2426407 C8.12649589,25.8000775 8.12649589,23.7354564 6.956685,22.2928932 C6.84095926,22.1501849 6.86283293,21.9406828 7.00554121,21.824957 C7.12767863,21.7259127 7.30248378,21.7259127 7.4246212,21.824957 C8.8671844,22.9947679 10.9318055,22.9947679 12.3743687,21.824957 C12.517077,21.7092313 12.7265791,21.7311049 12.8423049,21.8738132 C12.9413492,21.9959506 12.9413492,22.1707558 12.8423049,22.2928932 C11.672494,23.7354564 11.672494,25.8000775 12.8423049,27.2426407 C12.9580306,27.385349 12.9361569,27.5948511 12.7934487,27.7105769 C12.6713112,27.8096212 12.4965061,27.8096212 12.3743687,27.7105769 C10.9318055,26.540766 8.8671844,26.540766 7.4246212,27.7105769 Z" transform="translate(9.8995, 24.7678) rotate(45) translate(-9.8995, -24.7678)" data-v-23724bc4></path><circle id="椭圆形备份-14" cx="40.5961941" cy="51" r="3" data-v-23724bc4></circle><path id="星形备份-9" d="M47.1790153,41.759536 L45.3085047,43.0930273 C44.866681,43.4080044 44.2531728,43.3051749 43.9381957,42.8633512 C43.6948913,42.522064 43.6948913,42.0640054 43.9381957,41.7227182 L45.2716869,39.8522077 C46.5099141,38.1153256 46.5099141,35.7841693 45.2716869,34.0472873 L43.9381957,32.1767767 C43.6232186,31.734953 43.726048,31.1214448 44.1678717,30.8064677 C44.5091589,30.5631633 44.9672175,30.5631633 45.3085047,30.8064677 L47.1790153,32.139959 C48.9158973,33.3781861 51.2470536,33.3781861 52.9839356,32.139959 L54.8544462,30.8064677 C55.2962699,30.4914906 55.9097781,30.59432 56.2247552,31.0361437 C56.4680597,31.3774309 56.4680597,31.8354895 56.2247552,32.1767767 L54.891264,34.0472873 C53.6530368,35.7841693 53.6530368,38.1153256 54.891264,39.8522077 L56.2247552,41.7227182 C56.5397323,42.1645419 56.4369029,42.7780502 55.9950792,43.0930273 C55.653792,43.3363317 55.1957334,43.3363317 54.8544462,43.0930273 L52.9839356,41.759536 C51.2470536,40.5213088 48.9158973,40.5213088 47.1790153,41.759536 Z" transform="translate(50.0815, 36.9497) rotate(45) translate(-50.0815, -36.9497)" data-v-23724bc4></path></g>', 1)),
          createBaseVNode("g", {
            id: "白板备份",
            fill: unref(brushColor),
            "fill-rule": "nonzero",
            transform: "translate(1, 40)"
          }, _cache[7] || (_cache[7] = [
            createBaseVNode("path", {
              id: "路径-7备份-5",
              d: "M31.7760026,23.2802075 L32.6717223,28.1993219 C30.8346651,28.533831 29.0667516,28.7393091 27.3677243,28.8145869 L27.1464094,23.8194873 C28.6130277,23.7545067 30.1563112,23.5751365 31.7760026,23.2802075 Z M20.0633323,23.0917616 C21.4300537,23.4771718 22.8957782,23.7160344 24.4631191,23.8062327 L24.1758511,28.7979736 C22.253864,28.6873658 20.4298016,28.3901066 18.7062771,27.9040792 L20.0633323,23.0917616 Z M39.1652799,21.3117929 L40.77981,26.0439481 C39.0636618,26.6294684 37.3983548,27.1236017 35.7835314,27.5260284 L34.5744706,22.674413 C36.0492518,22.3068859 37.5796407,21.8527858 39.1652799,21.3117929 Z M13.9059594,19.8477916 C15.0261742,20.7917529 16.2426554,21.5654754 17.5603078,22.1725009 L15.4682046,26.7137677 C13.7426626,25.9188327 12.1463053,24.9034964 10.6840377,23.6713006 L13.9059594,19.8477916 Z M9.44645509,14.3410143 C10.2285576,15.7186248 11.0907103,16.940294 12.0319034,18.008773 L8.27995861,21.3137557 C7.10642086,19.98151 6.04621037,18.4791931 5.09831746,16.8095562 L9.44645509,14.3410143 Z M6.5894847,7.47395894 C7.06520508,9.05631272 7.60370006,10.5216245 8.20393772,11.8700675 L3.63604949,13.9033892 C2.94755894,12.3566847 2.33628603,10.6933352 1.80119882,8.91351388 L6.5894847,7.47395894 Z M4.94185618,0 C5.19691562,1.65785286 5.50026207,3.22919191 5.85150296,4.71385009 L0.985816002,5.86497581 C0.603294592,4.24809818 0.274820113,2.54659557 -1.13686838e-13,0.76030093 L4.94185618,0 Z"
            }, null, -1)
          ]), 8, _hoisted_4$2)
        ])
      ], 2)) : unref(brushType) === unref(IPencilModes).MARKER ? (openBlock(), createElementBlock("svg", {
        key: 3,
        viewBox: "0 0 70 70",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        class: normalizeClass(_ctx.$attrs.class)
      }, [
        _cache[11] || (_cache[11] = createBaseVNode("title", null, "形状", -1)),
        createBaseVNode("g", _hoisted_5$2, [
          _cache[10] || (_cache[10] = createStaticVNode('<path id="星形备份-8" d="M7.4246212,31.7105769 C7.28191292,31.8263026 7.07241075,31.804429 6.956685,31.6617207 C6.85764069,31.5395833 6.85764069,31.3647781 6.956685,31.2426407 C8.12649589,29.8000775 8.12649589,27.7354564 6.956685,26.2928932 C6.84095926,26.1501849 6.86283293,25.9406828 7.00554121,25.824957 C7.12767863,25.7259127 7.30248378,25.7259127 7.4246212,25.824957 C8.8671844,26.9947679 10.9318055,26.9947679 12.3743687,25.824957 C12.517077,25.7092313 12.7265791,25.7311049 12.8423049,25.8738132 C12.9413492,25.9959506 12.9413492,26.1707558 12.8423049,26.2928932 C11.672494,27.7354564 11.672494,29.8000775 12.8423049,31.2426407 C12.9580306,31.385349 12.9361569,31.5948511 12.7934487,31.7105769 C12.6713112,31.8096212 12.4965061,31.8096212 12.3743687,31.7105769 C10.9318055,30.540766 8.8671844,30.540766 7.4246212,31.7105769 Z" transform="translate(9.8995, 28.7678) rotate(45) translate(-9.8995, -28.7678)" data-v-23724bc4></path><circle id="椭圆形备份-14" cx="60.5961941" cy="60" r="3" data-v-23724bc4></circle><path id="星形备份-9" d="M62.1790153,46.759536 L60.3085047,48.0930273 C59.866681,48.4080044 59.2531728,48.3051749 58.9381957,47.8633512 C58.6948913,47.522064 58.6948913,47.0640054 58.9381957,46.7227182 L60.2716869,44.8522077 C61.5099141,43.1153256 61.5099141,40.7841693 60.2716869,39.0472873 L58.9381957,37.1767767 C58.6232186,36.734953 58.726048,36.1214448 59.1678717,35.8064677 C59.5091589,35.5631633 59.9672175,35.5631633 60.3085047,35.8064677 L62.1790153,37.139959 C63.9158973,38.3781861 66.2470536,38.3781861 67.9839356,37.139959 L69.8544462,35.8064677 C70.2962699,35.4914906 70.9097781,35.59432 71.2247552,36.0361437 C71.4680597,36.3774309 71.4680597,36.8354895 71.2247552,37.1767767 L69.891264,39.0472873 C68.6530368,40.7841693 68.6530368,43.1153256 69.891264,44.8522077 L71.2247552,46.7227182 C71.5397323,47.1645419 71.4369029,47.7780502 70.9950792,48.0930273 C70.653792,48.3363317 70.1957334,48.3363317 69.8544462,48.0930273 L67.9839356,46.759536 C66.2470536,45.5213088 63.9158973,45.5213088 62.1790153,46.759536 Z" transform="translate(65.0815, 41.9497) rotate(45) translate(-65.0815, -41.9497)" data-v-23724bc4></path><g id="编组-18" transform="translate(15.5962, 21.228)" data-v-23724bc4><path id="形状结合备份-5" d="M31.9498941,13.2900889 C32.6263937,13.2900889 33.2043182,13.7778795 33.3179367,14.4447697 L37.9979136,41.9141575 C38.126637,42.6697066 37.6184943,43.386551 36.8629453,43.5152744 C36.785947,43.5283926 36.7079787,43.5349869 36.629871,43.5349869 L1.3877551,43.5349869 C0.621319123,43.5349869 -1.31006317e-14,42.9136677 -1.28785871e-14,42.1472318 C-1.28785871e-14,42.0722433 0.00607807166,41.9973783 0.0181745144,41.9233719 L4.50808433,14.4539841 C4.61778082,13.7828581 5.19763291,13.2900889 5.87766491,13.2900889 L31.9498941,13.2900889 Z M29.6477322,16.5349869 L8.21308699,16.5349869 C7.86715016,16.5349869 7.57405748,16.7897976 7.52595123,17.1323733 L6.68370439,23.1302066 C6.67921532,23.1621744 6.67696257,23.1944164 6.67696257,23.2266978 C6.67696257,23.6099158 6.98762214,23.9205754 7.37084015,23.9205754 L30.6227894,23.9205754 C30.6600843,23.9205754 30.6973185,23.9175686 30.7341301,23.9115842 C31.1123824,23.8500924 31.3691675,23.4936093 31.3076757,23.115357 L30.3326185,17.1175237 C30.2780227,16.7816906 29.987974,16.5349869 29.6477322,16.5349869 Z M26.6075573,0 C27.4172728,0 27.9561175,0.609367376 28.0597377,1.0044386 L29.7447724,11.819546 L7.98453643,11.819546 L9.25497696,1.0044386 C9.35859715,0.609367376 9.85821822,0 10.6754418,0 L26.6075573,0 Z" data-v-23724bc4></path></g>', 4)),
          createBaseVNode("g", _hoisted_6$2, [
            createBaseVNode("polygon", {
              id: "路径",
              fill: unref(brushColor),
              points: "12.7893671 9.08196562 1.60291394e-14 9.08196562 0.482926121 3.18483027 12.1683366 4.37842199e-15"
            }, null, 8, _hoisted_7$2)
          ])
        ])
      ], 2)) : createCommentVNode("", true);
    };
  }
});
const WhiteboardTooldicsPencilIcon_vue_vue_type_style_index_0_scoped_23724bc4_lang = "";
const WhiteboardTooldicsPencilIcon = /* @__PURE__ */ _export_sfc$1(_sfc_main$5, [["__scopeId", "data-v-23724bc4"]]);
const _hoisted_1$3 = {
  key: 0,
  class: "whiteboard-tooldics-init"
};
const _hoisted_2$3 = {
  key: 1,
  class: "whiteboard-tooldics-tool-item flex flex-jc flex-ac"
};
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "WhiteboardTooldicsCenter",
  props: {
    isExpand: { type: Boolean, default: false }
  },
  setup(__props) {
    const whiteboardStore = useWhiteboardStore();
    const { currentTool, isPencilTool, currentSecondTool } = storeToRefs(whiteboardStore);
    const props = __props;
    const showTool = computed(() => {
      if (currentTool.value && currentTool.value.childClass?.length && currentSecondTool.value) {
        return currentSecondTool.value;
      }
      return currentTool.value;
    });
    return (_ctx, _cache) => {
      const _component_SvgIcon = SvgIcon;
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["whiteboard-tooldics-center", {
          "is-expand": props.isExpand
        }])
      }, [
        props.isExpand || !unref(currentTool) ? (openBlock(), createElementBlock("div", _hoisted_1$3)) : (openBlock(), createElementBlock("div", _hoisted_2$3, [
          unref(isPencilTool) ? (openBlock(), createBlock(WhiteboardTooldicsPencilIcon, {
            key: 0,
            class: "whiteboard-tooldics-tool-icon"
          })) : showTool.value ? (openBlock(), createBlock(_component_SvgIcon, {
            key: 1,
            class: "whiteboard-tooldics-tool-icon",
            "icon-class": showTool.value.iconClass
          }, null, 8, ["icon-class"])) : createCommentVNode("", true)
        ]))
      ], 2);
    };
  }
});
const WhiteboardTooldicsCenter_vue_vue_type_style_index_0_scoped_e3099f66_lang = "";
const WhiteboardTooldicsCenter = /* @__PURE__ */ _export_sfc$1(_sfc_main$4, [["__scopeId", "data-v-e3099f66"]]);
const elColorPicker = "";
const elInput = "";
const _hoisted_1$2 = { class: "whiteboard-tooldics-pencil-setting" };
const _hoisted_2$2 = {
  viewBox: "0 0 750 375",
  version: "1.1",
  xmlns: "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink"
};
const _hoisted_3$2 = {
  id: "V1.2.0",
  stroke: "none",
  "stroke-width": "1",
  fill: "none",
  "fill-rule": "evenodd"
};
const _hoisted_4$1 = {
  id: "画笔颜色",
  transform: "translate(591.264, 211.0306) rotate(-27) translate(-591.264, -211.0306)translate(502.9716, 31.1717)"
};
const _hoisted_5$1 = {
  key: 0,
  id: "画笔颜色-白-active",
  stroke: "#FFFFFF",
  "stroke-width": "2",
  cx: "26",
  cy: "26",
  r: "32"
};
const _hoisted_6$1 = {
  key: 1,
  id: "画笔颜色-红-active",
  stroke: "#FFFFFF",
  "stroke-width": "2",
  cx: "92",
  cy: "88",
  r: "32"
};
const _hoisted_7$1 = {
  key: 2,
  id: "画笔颜色-蓝-active",
  stroke: "#FFFFFF",
  "stroke-width": "2",
  cx: "129.451903",
  cy: "162.076683",
  r: "32"
};
const _hoisted_8$1 = {
  key: 3,
  id: "画笔颜色-黄-active",
  stroke: "#FFFFFF",
  "stroke-width": "2",
  cx: "143.584865",
  cy: "244.473645",
  r: "32"
};
const _hoisted_9 = {
  key: 4,
  id: "画笔颜色-自定义-active",
  stroke: "#FFFFFF",
  "stroke-width": "2",
  cx: "138",
  cy: "325",
  r: "32"
};
const _hoisted_10 = {
  id: "画笔大小",
  transform: "translate(184.9535, 245.2499) rotate(6) translate(-184.9535, -245.2499)translate(113.3321, 140.4672)"
};
const _hoisted_11 = {
  key: 0,
  id: "画笔大小-背景-b",
  d: "M39.3457015,132.059745 C51.942699,119.746837 67.9306535,119.746837 77.3936103,129.801138 C86.8565671,139.85544 86.8565671,154.727084 73.6140997,168.469591 C70.5485066,171.650942 63.7852742,179.514667 61.8336435,181.885575 C53.0924533,192.504675 39.3457015,201.351575 24.942699,191.962697 C10.5396964,182.573818 12.3528903,166.319857 19.8525006,154.727084 C22.701595,150.323002 25.8217748,146.198201 29.2118269,142.363189 C31.818363,139.414533 35.3412991,135.97384 39.3457015,132.059745 Z",
  fill: "#5F52E3",
  "fill-rule": "nonzero",
  transform: "translate(49.1076, 159.2276) rotate(-32) translate(-49.1076, -159.2276)"
};
const _hoisted_12 = {
  key: 1,
  id: "画笔大小-背景-m",
  d: "M58.0415696,70.7697416 C70.638567,58.4568334 86.6265215,58.4568334 96.0894783,68.5111347 C105.552435,78.5654361 105.552435,93.4370809 92.3099677,107.179588 C89.2443747,110.360938 82.4811423,118.224664 80.5295116,120.595572 C71.7883214,131.214671 58.0415696,140.061571 43.638567,130.672693 C29.2355645,121.283815 31.0487584,105.029853 38.5483686,93.4370809 C41.397463,89.0329982 44.5176428,84.9081976 47.9076949,81.0731855 C50.514231,78.1245295 54.0371672,74.6838362 58.0415696,70.7697416 Z",
  fill: "#5F52E3",
  "fill-rule": "nonzero",
  transform: "translate(67.8035, 97.9376) rotate(-17) translate(-67.8035, -97.9376)"
};
const _hoisted_13 = {
  key: 2,
  id: "画笔大小-背景-s",
  d: "M91.4969719,15.9725149 C104.093969,3.65960665 120.081924,3.65960665 129.544881,13.713908 C139.007837,23.7682094 139.007837,38.6398542 125.76537,52.382361 C122.699777,55.5637117 115.936545,63.427437 113.984914,65.7983452 C105.243724,76.4174444 91.4969719,85.2643447 77.0939693,75.8754664 C62.6909668,66.4865881 64.5041607,50.2326267 72.0037709,38.6398542 C74.8528653,34.2357715 77.9730451,30.1109709 81.3630972,26.2759588 C83.9696333,23.3273027 87.4925695,19.8866095 91.4969719,15.9725149 Z",
  fill: "#5F52E3",
  "fill-rule": "nonzero"
};
const _hoisted_14 = {
  id: "画笔类型",
  transform: "translate(0.0167, 0.0545)"
};
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "WhiteboardTooldicsPencilSetting",
  emits: ["hide-color-picker", "show-color-picker"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    const elColorPickerRef = ref();
    const colorPickerVisible = ref(false);
    const whiteboardStore = useWhiteboardStore();
    const { brushColorType, brushSizeType, brushColor, brushType } = storeToRefs(whiteboardStore);
    const onClickChangeBrushSize = (size) => {
      whiteboardStore.setBrushSizeType(size);
      hideColorPicker();
    };
    const onClickChangeBrushColor = (color) => {
      whiteboardStore.setBrushColorType(color);
      hideColorPicker();
    };
    const onClickChangeBrushType = (type) => {
      whiteboardStore.setBrushType(type);
      hideColorPicker();
    };
    const onChangeCustomBrushColor = (color) => {
      whiteboardStore.setBrushColorType(WhiteboardColorEnum.CUSTOM, color);
    };
    const hideColorPicker = () => {
      if (!elColorPickerRef.value) {
        return;
      }
      elColorPickerRef.value.hide();
      emits("hide-color-picker");
    };
    const showColorPicker = () => {
      if (!elColorPickerRef.value) {
        return;
      }
      elColorPickerRef.value.show();
      emits("show-color-picker");
    };
    const onClickTriggerColorPicker = () => {
      if (colorPickerVisible.value) {
        hideColorPicker();
      } else {
        showColorPicker();
      }
    };
    const onHideColorPicker = () => {
      colorPickerVisible.value = false;
      emits("hide-color-picker");
    };
    const onShowColorPicker = () => {
      colorPickerVisible.value = true;
      emits("show-color-picker");
    };
    return (_ctx, _cache) => {
      const _component_el_color_picker = ElColorPicker;
      return openBlock(), createElementBlock("div", _hoisted_1$2, [
        (openBlock(), createElementBlock("svg", _hoisted_2$2, [
          _cache[23] || (_cache[23] = createStaticVNode('<title data-v-45be4ea9>画笔设置</title><defs data-v-45be4ea9><path id="path-1" d="M69.5320348,305 C66.9635608,322.757686 67.8746375,357.335075 109.292928,364.181884 C150.711218,371.028694 162.819315,342.841653 169.03109,318.729767 C195.983259,214.111091 593.29139,213.789856 618.426267,318.729767 C625.71584,349.164255 643.924192,373.32569 683.382893,364.181884 C722.841594,355.038079 721.196032,318.729767 717.95999,305 C700.31095,165.824564 686.859305,23.1788861 686.859305,23.1788861 C686.859305,23.1788861 541.477683,15.4525907 250.71444,0 L17.6924667,73.3052123 L0,243.667262 C49.7209993,261.282065 72.8983442,281.726311 69.5320348,305 Z" data-v-45be4ea9></path><path id="path-3" d="M92.7870308,8.05558668 C104.906659,-3.24181133 123.88991,-2.5752617 135.187308,9.54436694 C146.484706,21.6639956 145.818157,40.6472464 133.698528,51.9446445 C120.379318,64.360241 109.178106,79.4247979 100.064513,97.2958199 C85.2200295,126.404658 77.3200003,154.241802 76.0920348,180.943106 C75.3308691,197.494155 61.2965461,210.294385 44.7454969,209.533219 C28.1944478,208.772054 15.3942182,194.737731 16.1553839,178.186681 C17.793857,142.559158 28.0374011,106.464227 46.6136251,70.0377467 C58.8462506,46.0505523 74.2475123,25.3373307 92.7870308,8.05558668 Z" data-v-45be4ea9></path><filter id="filter-4" x="-2.4%" y="-1.4%" width="104.7%" height="102.9%" filterUnits="objectBoundingBox" data-v-45be4ea9><feGaussianBlur stdDeviation="2.5" in="SourceAlpha" result="shadowBlurInner1" data-v-45be4ea9></feGaussianBlur><feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1" data-v-45be4ea9></feOffset><feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1" data-v-45be4ea9></feComposite><feColorMatrix values="0 0 0 0 0.37254902   0 0 0 0 0.321568627   0 0 0 0 0.890196078  0 0 0 0.592848558 0" type="matrix" in="shadowInnerInner1" data-v-45be4ea9></feColorMatrix></filter></defs>', 2)),
          createBaseVNode("g", _hoisted_3$2, [
            _cache[22] || (_cache[22] = createStaticVNode('<g id="画笔背景" transform="translate(30.1516, 9)" data-v-45be4ea9><mask id="mask-2" fill="white" data-v-45be4ea9><use xlink:href="#path-1" data-v-45be4ea9></use></mask><g id="蒙版" data-v-45be4ea9></g><path id="椭圆形" d="M393.848379,37 C576.102347,37 723.848379,184.746033 723.848379,367 C723.848379,549.253967 576.102347,697 393.848379,697 C211.594412,697 63.8483795,549.253967 63.8483795,367 C63.8483795,184.746033 211.594412,37 393.848379,37 Z M393.848379,137 C266.822887,137 163.848379,239.974508 163.848379,367 C163.848379,494.025492 266.822887,597 393.848379,597 C520.873872,597 623.848379,494.025492 623.848379,367 C623.848379,239.974508 520.873872,137 393.848379,137 Z" fill-opacity="0.65" fill="#000000" mask="url(#mask-2)" data-v-45be4ea9></path></g>', 1)),
            createBaseVNode("g", _hoisted_4$1, [
              unref(brushColorType) === unref(WhiteboardColorEnum).WHITE ? (openBlock(), createElementBlock("circle", _hoisted_5$1)) : createCommentVNode("", true),
              unref(brushColorType) === unref(WhiteboardColorEnum).RED ? (openBlock(), createElementBlock("circle", _hoisted_6$1)) : createCommentVNode("", true),
              unref(brushColorType) === unref(WhiteboardColorEnum).BLUE ? (openBlock(), createElementBlock("circle", _hoisted_7$1)) : createCommentVNode("", true),
              unref(brushColorType) === unref(WhiteboardColorEnum).YELLOW ? (openBlock(), createElementBlock("circle", _hoisted_8$1)) : createCommentVNode("", true),
              unref(brushColorType) === unref(WhiteboardColorEnum).CUSTOM ? (openBlock(), createElementBlock("circle", _hoisted_9)) : createCommentVNode("", true),
              createBaseVNode("circle", {
                id: "画笔颜色-白",
                stroke: "#DFDDFA",
                fill: "#FFFFFF",
                cx: "26",
                cy: "26",
                r: "25.5",
                onClick: _cache[0] || (_cache[0] = ($event) => onClickChangeBrushColor(unref(WhiteboardColorEnum).WHITE))
              }),
              createBaseVNode("circle", {
                id: "画笔颜色-红",
                fill: "#E93B26",
                cx: "92",
                cy: "88",
                r: "26",
                onClick: _cache[1] || (_cache[1] = ($event) => onClickChangeBrushColor(unref(WhiteboardColorEnum).RED))
              }),
              createBaseVNode("circle", {
                id: "画笔颜色-蓝",
                fill: "#0497E8",
                cx: "129",
                cy: "162",
                r: "26",
                onClick: _cache[2] || (_cache[2] = ($event) => onClickChangeBrushColor(unref(WhiteboardColorEnum).BLUE))
              }),
              createBaseVNode("circle", {
                id: "画笔颜色-黄",
                fill: "#F8BD18",
                cx: "143.745965",
                cy: "244.569147",
                r: "26",
                onClick: _cache[3] || (_cache[3] = ($event) => onClickChangeBrushColor(unref(WhiteboardColorEnum).YELLOW))
              })
            ]),
            createBaseVNode("g", _hoisted_10, [
              _cache[13] || (_cache[13] = createBaseVNode("g", {
                id: "画笔大小区",
                opacity: "0.25",
                "fill-rule": "nonzero"
              }, [
                createBaseVNode("use", {
                  fill: "#FFFFFF",
                  "xlink:href": "#path-3"
                }),
                createBaseVNode("use", {
                  fill: "black",
                  "fill-opacity": "1",
                  filter: "url(#filter-4)",
                  "xlink:href": "#path-3"
                })
              ], -1)),
              unref(brushSizeType) === unref(WhiteboardSizeEnum).LARGE ? (openBlock(), createElementBlock("path", _hoisted_11)) : createCommentVNode("", true),
              unref(brushSizeType) === unref(WhiteboardSizeEnum).MEDIUM ? (openBlock(), createElementBlock("path", _hoisted_12)) : createCommentVNode("", true),
              unref(brushSizeType) === unref(WhiteboardSizeEnum).SMALL ? (openBlock(), createElementBlock("path", _hoisted_13)) : createCommentVNode("", true),
              _cache[14] || (_cache[14] = createBaseVNode("circle", {
                id: "画笔大小-b",
                fill: "#FFFFFF",
                cx: "49.0939693",
                cy: "158.516945",
                r: "10"
              }, null, -1)),
              _cache[15] || (_cache[15] = createBaseVNode("circle", {
                id: "画笔大小-m",
                fill: "#FFFFFF",
                cx: "66.0939693",
                cy: "97.5169447",
                r: "7"
              }, null, -1)),
              _cache[16] || (_cache[16] = createBaseVNode("circle", {
                id: "画笔大小-s",
                fill: "#FFFFFF",
                cx: "100.093969",
                cy: "44.6848452",
                r: "4"
              }, null, -1)),
              createBaseVNode("circle", {
                id: "画笔大小-点击区-b",
                fill: "transparent",
                cx: "49.0939693",
                cy: "158.516945",
                r: "30",
                onClick: _cache[4] || (_cache[4] = ($event) => onClickChangeBrushSize(unref(WhiteboardSizeEnum).LARGE))
              }),
              createBaseVNode("circle", {
                id: "画笔大小-点击区-m",
                fill: "transparent",
                cx: "66.0939693",
                cy: "97.5169447",
                r: "30",
                onClick: _cache[5] || (_cache[5] = ($event) => onClickChangeBrushSize(unref(WhiteboardSizeEnum).MEDIUM))
              }),
              createBaseVNode("circle", {
                id: "画笔大小-点击区-s",
                fill: "transparent",
                cx: "100.093969",
                cy: "44.6848452",
                r: "30",
                onClick: _cache[6] || (_cache[6] = ($event) => onClickChangeBrushSize(unref(WhiteboardSizeEnum).SMALL))
              })
            ]),
            createBaseVNode("g", _hoisted_14, [
              _cache[20] || (_cache[20] = createBaseVNode("path", {
                id: "画笔类型-区",
                d: "M191.774151,7.40237132 C215.304072,-7.05061893 246.095305,0.307687361 260.548295,23.8376084 C275.001285,47.3675295 267.642979,78.1587629 244.113058,92.6117531 C208.925729,114.225176 179.36568,140.442394 155.182633,171.398071 C130.727158,202.70247 111.611563,241.468826 98.0050062,288.020872 C90.2578685,314.526116 62.4908267,329.732566 35.9855825,321.985428 C9.48033831,314.23829 -5.72611152,286.471249 2.02102618,259.966005 C19.0448949,201.722326 43.7744555,151.570869 76.3788676,109.835366 C107.981362,69.3823744 146.529883,35.1931556 191.774151,7.40237132 Z",
                "fill-opacity": "0.65",
                fill: "#000000"
              }, null, -1)),
              _cache[21] || (_cache[21] = createBaseVNode("path", {
                id: "画笔类型-背景",
                d: "M55.4870204,256.447284 C70.3932065,211.599077 82.6502195,183.227976 114.794955,142.24375 C146.93969,101.259524 169.628557,82.5882842 203.594942,59.7289645",
                stroke: "#FFFFFF",
                "stroke-width": "66",
                opacity: "0.25",
                "stroke-linecap": "round"
              }, null, -1)),
              unref(brushType) === unref(IPencilModes).SOLID ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                _cache[17] || (_cache[17] = createStaticVNode('<path id="铅笔-选中背景" d="M43.2868664,246.632493 C51.4947857,238.938509 53.1107954,236.228739 68.4409041,224.251187 C83.7710127,212.273635 85.1251548,212.429117 93.5949417,206.728965" stroke="#5F52E3" stroke-width="54" stroke-linecap="round" transform="translate(68.4409, 226.6807) rotate(-29) translate(-68.4409, -226.6807)" data-v-45be4ea9></path><path id="铅笔-图标" d="M60.0496492,256.904673 C60.727988,256.904673 61.3069182,257.395053 61.4185005,258.064151 L63.9642214,273.329457 C64.0902951,274.085453 63.5796425,274.800511 62.8236467,274.926585 C62.7482054,274.939166 62.6718532,274.945489 62.5953701,274.945489 L43.3709705,274.945489 C42.6045345,274.945489 41.9832154,274.32417 41.9832154,273.557734 C41.9832154,273.484307 41.9890429,273.410996 42.0006431,273.338492 L44.442975,258.073186 C44.5506883,257.399946 45.1314999,256.904673 45.8133024,256.904673 L60.0496492,256.904673 Z M59.1339709,258.639367 L46.7716454,258.639367 C46.4338993,258.639367 46.1452135,258.882554 46.0878493,259.215393 L45.3703175,263.378658 C45.3636088,263.417583 45.3602361,263.45701 45.3602361,263.496509 C45.3602361,263.879727 45.6708957,264.190387 46.0541137,264.190387 L59.9275574,264.190387 C59.9711552,264.190387 60.0146559,264.186278 60.0574825,264.178114 C60.4339226,264.106359 60.6809182,263.743024 60.6091625,263.366584 L59.815576,259.203319 C59.7532013,258.876093 59.4670888,258.639367 59.1339709,258.639367 Z M52.8130232,241.086883 L54.0245939,245.65862 L54.6303793,245.65862 C54.8633737,245.65862 55.0497692,245.800013 55.096368,246.03567 L57.7525038,256.027507 L47.9667403,256.027507 L50.2500852,246.03567 C50.296684,245.800013 50.4830795,245.65862 50.7160739,245.65862 L51.2286615,245.65862 L52.4868311,241.086883 C52.53343,240.898358 52.7664243,240.898358 52.8130232,241.086883 Z" fill="#FFFFFF" transform="translate(52.9833, 257.9455) rotate(-69) translate(-52.9833, -257.9455)" data-v-45be4ea9></path><g transform="translate(77.9985, 227.048) rotate(-19) translate(-77.9985, -227.048)translate(44.5167, 180.2992)" data-v-45be4ea9><text id="铅笔-文本" transform="translate(35.6581, 30.9105) rotate(-46.5) translate(-35.6581, -30.9105)" font-size="20" font-weight="500" fill="#FFFFFF" data-v-45be4ea9><tspan x="5.65805676" y="37.9105083" data-v-45be4ea9>实线笔</tspan></text></g><g id="马克笔-图标" transform="translate(124.4469, 128.9143) rotate(-49) translate(-124.4469, -128.9143)translate(113.4469, 114.7273)" fill="#FFFFFF" data-v-45be4ea9><path id="形状结合" d="M14.4427907,0.54891631 C14.51355,0.688831709 14.550418,0.843425152 14.550418,1.00021546 L14.550418,5.37387804 L15.550418,5.37387804 C16.1027027,5.37387804 16.550418,5.82159329 16.550418,6.37387804 L16.55,10.333 L18.0664338,10.3330617 C18.7447725,10.3330617 19.3237028,10.8234416 19.4352851,11.4925401 L21.981006,26.7578463 C22.1070797,27.513842 21.596427,28.2289005 20.8404313,28.3549743 C20.76499,28.3675553 20.6886378,28.373878 20.6121546,28.373878 L1.3877551,28.373878 C0.621319123,28.373878 1.77944669e-14,27.7525589 1.77944669e-14,26.9861229 C1.77944669e-14,26.9126963 0.00582752509,26.8393856 0.0174276878,26.7668811 L2.4597596,11.5015749 C2.56747289,10.8283346 3.14828451,10.3330617 3.83008702,10.3330617 L5.55,10.333 L5.55041797,6.37387804 C5.55041797,5.82159329 5.99813322,5.37387804 6.55041797,5.37387804 L7.5925608,5.37387804 L7.5925608,3.50754979 C7.5925608,3.13042456 7.80472537,2.78537336 8.14126164,2.61517707 L13.0991188,0.107842744 C13.5919627,-0.141402897 14.1935451,0.0560724655 14.4427907,0.54891631 Z M17.1766579,12.0677556 L4.81030372,12.0677556 C4.46300821,12.0677556 4.16920296,12.3245175 4.12268003,12.6686829 L3.98819962,13.6635364 C3.98387337,13.695541 3.98178681,13.7278085 3.98195516,13.7601037 C3.98395273,14.1433165 4.29622744,14.4523525 4.67944024,14.4503549 L17.4011139,14.3840406 C17.453971,14.383765 17.5066235,14.3774507 17.5580469,14.3652204 C17.9308656,14.2765511 18.1612142,13.9024412 18.0725449,13.5296224 L17.8517059,12.6010832 C17.7773468,12.288433 17.498029,12.0677556 17.1766579,12.0677556 Z" data-v-45be4ea9></path></g><path id="虚线-图标" d="M182.002415,79.9043266 C186.185584,71.3176835 193.495073,64.7323598 203.930881,60.1483557" stroke="#FFFFFF" stroke-width="5" stroke-dasharray="5,3" transform="translate(192.9666, 70.0263) rotate(7) translate(-192.9666, -70.0263)" data-v-45be4ea9></path>', 5)),
                createBaseVNode("circle", {
                  id: "马克笔-点击区",
                  fill: "transparent",
                  cx: "126",
                  cy: "129",
                  r: "32",
                  onClick: _cache[7] || (_cache[7] = ($event) => onClickChangeBrushType(unref(IPencilModes).MARKER))
                }),
                createBaseVNode("circle", {
                  id: "虚线-点击区",
                  fill: "transparent",
                  cx: "192",
                  cy: "68",
                  r: "32",
                  onClick: _cache[8] || (_cache[8] = ($event) => onClickChangeBrushType(unref(IPencilModes).DASHED))
                })
              ], 64)) : createCommentVNode("", true),
              unref(brushType) === unref(IPencilModes).MARKER ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                _cache[18] || (_cache[18] = createStaticVNode('<path id="铅笔-图标" d="M72.0496492,229.904673 C72.727988,229.904673 73.3069182,230.395053 73.4185005,231.064151 L75.9642214,246.329457 C76.0902951,247.085453 75.5796425,247.800511 74.8236467,247.926585 C74.7482054,247.939166 74.6718532,247.945489 74.5953701,247.945489 L55.3709705,247.945489 C54.6045345,247.945489 53.9832154,247.32417 53.9832154,246.557734 C53.9832154,246.484307 53.9890429,246.410996 54.0006431,246.338492 L56.442975,231.073186 C56.5506883,230.399946 57.1314999,229.904673 57.8133024,229.904673 L72.0496492,229.904673 Z M71.1339709,231.639367 L58.7716454,231.639367 C58.4338993,231.639367 58.1452135,231.882554 58.0878493,232.215393 L57.3703175,236.378658 C57.3636088,236.417583 57.3602361,236.45701 57.3602361,236.496509 C57.3602361,236.879727 57.6708957,237.190387 58.0541137,237.190387 L71.9275574,237.190387 C71.9711552,237.190387 72.0146559,237.186278 72.0574825,237.178114 C72.4339226,237.106359 72.6809182,236.743024 72.6091625,236.366584 L71.815576,232.203319 C71.7532013,231.876093 71.4670888,231.639367 71.1339709,231.639367 Z M64.8130232,214.086883 L66.0245939,218.65862 L66.6303793,218.65862 C66.8633737,218.65862 67.0497692,218.800013 67.096368,219.03567 L69.7525038,229.027507 L59.9667403,229.027507 L62.2500852,219.03567 C62.296684,218.800013 62.4830795,218.65862 62.7160739,218.65862 L63.2286615,218.65862 L64.4868311,214.086883 C64.53343,213.898358 64.7664243,213.898358 64.8130232,214.086883 Z" fill="#FFFFFF" transform="translate(64.9833, 230.9455) rotate(-62) translate(-64.9833, -230.9455)" data-v-45be4ea9></path><path id="马克笔-选中背景" d="M98.2868664,154.632493 C106.494786,146.938509 108.110795,144.228739 123.440904,132.251187 C138.771013,120.273635 140.125155,120.429117 148.594942,114.728965" stroke="#5F52E3" stroke-width="54" stroke-linecap="round" transform="translate(123.4409, 134.6807) rotate(-11) translate(-123.4409, -134.6807)" data-v-45be4ea9></path><g id="马克笔-图标" transform="translate(102.4469, 156.9143) rotate(-54) translate(-102.4469, -156.9143)translate(91.4469, 142.7273)" fill="#FFFFFF" data-v-45be4ea9><path id="形状结合" d="M14.4427907,0.54891631 C14.51355,0.688831709 14.550418,0.843425152 14.550418,1.00021546 L14.550418,5.37387804 L15.550418,5.37387804 C16.1027027,5.37387804 16.550418,5.82159329 16.550418,6.37387804 L16.55,10.333 L18.0664338,10.3330617 C18.7447725,10.3330617 19.3237028,10.8234416 19.4352851,11.4925401 L21.981006,26.7578463 C22.1070797,27.513842 21.596427,28.2289005 20.8404313,28.3549743 C20.76499,28.3675553 20.6886378,28.373878 20.6121546,28.373878 L1.3877551,28.373878 C0.621319123,28.373878 1.77944669e-14,27.7525589 1.77944669e-14,26.9861229 C1.77944669e-14,26.9126963 0.00582752509,26.8393856 0.0174276878,26.7668811 L2.4597596,11.5015749 C2.56747289,10.8283346 3.14828451,10.3330617 3.83008702,10.3330617 L5.55,10.333 L5.55041797,6.37387804 C5.55041797,5.82159329 5.99813322,5.37387804 6.55041797,5.37387804 L7.5925608,5.37387804 L7.5925608,3.50754979 C7.5925608,3.13042456 7.80472537,2.78537336 8.14126164,2.61517707 L13.0991188,0.107842744 C13.5919627,-0.141402897 14.1935451,0.0560724655 14.4427907,0.54891631 Z M17.1766579,12.0677556 L4.81030372,12.0677556 C4.46300821,12.0677556 4.16920296,12.3245175 4.12268003,12.6686829 L3.98819962,13.6635364 C3.98387337,13.695541 3.98178681,13.7278085 3.98195516,13.7601037 C3.98395273,14.1433165 4.29622744,14.4523525 4.67944024,14.4503549 L17.4011139,14.3840406 C17.453971,14.383765 17.5066235,14.3774507 17.5580469,14.3652204 C17.9308656,14.2765511 18.1612142,13.9024412 18.0725449,13.5296224 L17.8517059,12.6010832 C17.7773468,12.288433 17.498029,12.0677556 17.1766579,12.0677556 Z" data-v-45be4ea9></path></g><g id="马克笔-文本" transform="translate(99.5167, 88.2992)" data-v-45be4ea9><text transform="translate(35.6581, 30.9105) rotate(-50) translate(-35.6581, -30.9105)" font-size="20" font-weight="500" fill="#FFFFFF" data-v-45be4ea9><tspan x="5.65805676" y="37.9105083" data-v-45be4ea9>马克笔</tspan></text></g><path id="虚线-图标" d="M182.002415,79.9043266 C186.185584,71.3176835 193.495073,64.7323598 203.930881,60.1483557" stroke="#FFFFFF" stroke-width="5" stroke-dasharray="5,3" transform="translate(192.9666, 70.0263) rotate(7) translate(-192.9666, -70.0263)" data-v-45be4ea9></path>', 5)),
                createBaseVNode("circle", {
                  id: "铅笔-点击区",
                  fill: "transparent",
                  cx: "64.9832762",
                  cy: "230.945489",
                  r: "32",
                  onClick: _cache[9] || (_cache[9] = ($event) => onClickChangeBrushType(unref(IPencilModes).SOLID))
                }),
                createBaseVNode("circle", {
                  id: "虚线-点击区",
                  fill: "transparent",
                  cx: "192",
                  cy: "68",
                  r: "32",
                  onClick: _cache[10] || (_cache[10] = ($event) => onClickChangeBrushType(unref(IPencilModes).DASHED))
                })
              ], 64)) : createCommentVNode("", true),
              unref(brushType) === unref(IPencilModes).DASHED ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
                _cache[19] || (_cache[19] = createStaticVNode('<path id="虚线-选中背景" d="M153.286866,99.6324926 C161.494786,91.938509 163.110795,89.2287393 178.440904,77.251187 C193.771013,65.2736346 195.125155,65.4291169 203.594942,59.7289645" stroke="#5F52E3" stroke-width="54" stroke-linecap="round" data-v-45be4ea9></path><path id="铅笔-图标" d="M72.0496492,229.904673 C72.727988,229.904673 73.3069182,230.395053 73.4185005,231.064151 L75.9642214,246.329457 C76.0902951,247.085453 75.5796425,247.800511 74.8236467,247.926585 C74.7482054,247.939166 74.6718532,247.945489 74.5953701,247.945489 L55.3709705,247.945489 C54.6045345,247.945489 53.9832154,247.32417 53.9832154,246.557734 C53.9832154,246.484307 53.9890429,246.410996 54.0006431,246.338492 L56.442975,231.073186 C56.5506883,230.399946 57.1314999,229.904673 57.8133024,229.904673 L72.0496492,229.904673 Z M71.1339709,231.639367 L58.7716454,231.639367 C58.4338993,231.639367 58.1452135,231.882554 58.0878493,232.215393 L57.3703175,236.378658 C57.3636088,236.417583 57.3602361,236.45701 57.3602361,236.496509 C57.3602361,236.879727 57.6708957,237.190387 58.0541137,237.190387 L71.9275574,237.190387 C71.9711552,237.190387 72.0146559,237.186278 72.0574825,237.178114 C72.4339226,237.106359 72.6809182,236.743024 72.6091625,236.366584 L71.815576,232.203319 C71.7532013,231.876093 71.4670888,231.639367 71.1339709,231.639367 Z M64.8130232,214.086883 L66.0245939,218.65862 L66.6303793,218.65862 C66.8633737,218.65862 67.0497692,218.800013 67.096368,219.03567 L69.7525038,229.027507 L59.9667403,229.027507 L62.2500852,219.03567 C62.296684,218.800013 62.4830795,218.65862 62.7160739,218.65862 L63.2286615,218.65862 L64.4868311,214.086883 C64.53343,213.898358 64.7664243,213.898358 64.8130232,214.086883 Z" fill="#FFFFFF" transform="translate(64.9833, 230.9455) rotate(-62) translate(-64.9833, -230.9455)" data-v-45be4ea9></path><g id="马克笔-图标" transform="translate(106.4469, 151.9143) rotate(-54) translate(-106.4469, -151.9143)translate(95.4469, 137.7273)" fill="#FFFFFF" data-v-45be4ea9><path id="形状结合" d="M14.4427907,0.54891631 C14.51355,0.688831709 14.550418,0.843425152 14.550418,1.00021546 L14.550418,5.37387804 L15.550418,5.37387804 C16.1027027,5.37387804 16.550418,5.82159329 16.550418,6.37387804 L16.55,10.333 L18.0664338,10.3330617 C18.7447725,10.3330617 19.3237028,10.8234416 19.4352851,11.4925401 L21.981006,26.7578463 C22.1070797,27.513842 21.596427,28.2289005 20.8404313,28.3549743 C20.76499,28.3675553 20.6886378,28.373878 20.6121546,28.373878 L1.3877551,28.373878 C0.621319123,28.373878 1.77944669e-14,27.7525589 1.77944669e-14,26.9861229 C1.77944669e-14,26.9126963 0.00582752509,26.8393856 0.0174276878,26.7668811 L2.4597596,11.5015749 C2.56747289,10.8283346 3.14828451,10.3330617 3.83008702,10.3330617 L5.55,10.333 L5.55041797,6.37387804 C5.55041797,5.82159329 5.99813322,5.37387804 6.55041797,5.37387804 L7.5925608,5.37387804 L7.5925608,3.50754979 C7.5925608,3.13042456 7.80472537,2.78537336 8.14126164,2.61517707 L13.0991188,0.107842744 C13.5919627,-0.141402897 14.1935451,0.0560724655 14.4427907,0.54891631 Z M17.1766579,12.0677556 L4.81030372,12.0677556 C4.46300821,12.0677556 4.16920296,12.3245175 4.12268003,12.6686829 L3.98819962,13.6635364 C3.98387337,13.695541 3.98178681,13.7278085 3.98195516,13.7601037 C3.98395273,14.1433165 4.29622744,14.4523525 4.67944024,14.4503549 L17.4011139,14.3840406 C17.453971,14.383765 17.5066235,14.3774507 17.5580469,14.3652204 C17.9308656,14.2765511 18.1612142,13.9024412 18.0725449,13.5296224 L17.8517059,12.6010832 C17.7773468,12.288433 17.498029,12.0677556 17.1766579,12.0677556 Z" data-v-45be4ea9></path></g><path id="虚线-图标" d="M154.002415,101.904327 C158.185584,93.3176835 165.495073,86.7323598 175.930881,82.1483557" stroke="#FFFFFF" stroke-width="5" stroke-dasharray="5,3" data-v-45be4ea9></path><text id="虚线-文本" transform="translate(196.9833, 64.9455) rotate(-35) translate(-196.9833, -64.9455)" font-size="20" font-weight="500" fill="#FFFFFF" data-v-45be4ea9><tspan x="176.983276" y="71.945489" data-v-45be4ea9>虚线</tspan></text>', 5)),
                createBaseVNode("circle", {
                  id: "铅笔-点击区",
                  fill: "transparent",
                  cx: "64.9832762",
                  cy: "230.945489",
                  r: "32",
                  onClick: _cache[11] || (_cache[11] = ($event) => onClickChangeBrushType(unref(IPencilModes).SOLID))
                }),
                createBaseVNode("circle", {
                  id: "马克笔-点击区",
                  fill: "transparent",
                  cx: "105.983276",
                  cy: "151.945489",
                  r: "32",
                  onClick: _cache[12] || (_cache[12] = ($event) => onClickChangeBrushType(unref(IPencilModes).MARKER))
                })
              ], 64)) : createCommentVNode("", true)
            ])
          ])
        ])),
        createBaseVNode("div", {
          class: "whiteboard-color-picker",
          onClick: onClickTriggerColorPicker
        }, [
          createVNode(_component_el_color_picker, {
            ref_key: "elColorPickerRef",
            ref: elColorPickerRef,
            "show-picker": true,
            "popper-class": "whiteboard-color-popper",
            "model-value": unref(brushColor),
            onChange: onChangeCustomBrushColor,
            onHide: onHideColorPicker,
            onShow: onShowColorPicker
          }, null, 8, ["model-value"])
        ])
      ]);
    };
  }
});
const WhiteboardTooldicsPencilSetting_vue_vue_type_style_index_0_scoped_45be4ea9_lang = "";
const WhiteboardTooldicsPencilSetting_vue_vue_type_style_index_1_lang = "";
const WhiteboardTooldicsPencilSetting = /* @__PURE__ */ _export_sfc$1(_sfc_main$3, [["__scopeId", "data-v-45be4ea9"]]);
const _hoisted_1$1 = {
  id: "g44",
  fill: "none"
};
const _hoisted_2$1 = ["opacity"];
const _hoisted_3$1 = ["opacity"];
const _hoisted_4 = ["opacity"];
const _hoisted_5 = {
  key: 0,
  id: "circle39",
  cx: "222.183",
  cy: "158.66499",
  r: "36",
  stroke: "url(#v)",
  "stroke-width": "2",
  style: { "stroke": "url(#linearGradient44)" }
};
const _hoisted_6 = {
  key: 1,
  id: "circle40",
  cx: "157.183",
  cy: "99.665001",
  r: "36",
  stroke: "url(#v)",
  "stroke-width": "2",
  style: { "stroke": "url(#linearGradient45)" }
};
const _hoisted_7 = {
  key: 2,
  id: "circle41",
  cx: "86.182999",
  cy: "59.665001",
  r: "36",
  stroke: "url(#v)",
  "stroke-width": "2",
  style: { "stroke": "url(#linearGradient46)" }
};
const _hoisted_8 = ["transform"];
const scalc = 154 / 332;
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "WhiteboardTooldicsEraserSetting",
  setup(__props) {
    const whiteboardStore = useWhiteboardStore();
    const { eraserSizeType } = storeToRefs(whiteboardStore);
    const dragHandleRef = ref();
    const svgRef = ref();
    function calculateY(x) {
      const h = 0;
      const k = 0;
      const r = 129;
      if (x < -129) {
        return [k + r, k - r];
      } else if (x > 129) {
        return [k + r, k - r];
      }
      const part1 = Math.pow(x + h, 2);
      const part2 = Math.pow(r, 2) - part1;
      if (part2 < 0) {
        return [k + r, k - r];
      }
      const y1 = Math.sqrt(part2) - k;
      const y2 = -Math.sqrt(part2) - k;
      return [y1, y2];
    }
    const originPosition = {
      x: -93,
      y: -120
    };
    const { position } = useDraggable(dragHandleRef, {
      containerElement: svgRef,
      initialValue: {
        x: originPosition.x + 93,
        y: originPosition.y + 89.5
      },
      onMove(pointer) {
        let { x } = pointer;
        if (x < 16) {
          x = 16;
        }
        if (x > 81) {
          x = 81;
        }
        x += 12;
        position.value = {
          x: originPosition.x + x,
          y: originPosition.y + calculateY(x)[0]
        };
      },
      onEnd() {
        if (position.value.x < -60) {
          whiteboardStore.clearCanvas();
        }
        position.value = {
          x: originPosition.x + 93,
          y: originPosition.y + 89.5
        };
      }
    });
    const onClickChangeEraserSize = (size) => {
      whiteboardStore.setEraserSizeType(size);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("svg", {
        id: "svg44",
        ref_key: "svgRef",
        ref: svgRef,
        class: "whiteboard-tooldics-eraser-setting",
        viewBox: "0 0 332 654",
        version: "1.1",
        "sodipodi:docname": "Untitled-1.svg",
        "inkscape:version": "1.4.2 (f4327f4, 2025-05-13)",
        "xmlns:inkscape": "http://www.inkscape.org/namespaces/inkscape",
        "xmlns:sodipodi": "http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:svg": "http://www.w3.org/2000/svg"
      }, [
        _cache[10] || (_cache[10] = createBaseVNode("defs", { id: "defs21" }, [
          createBaseVNode("filter", {
            id: "x",
            width: "1.094399",
            height: "1.0620332",
            x: "-0.04719949",
            y: "-0.028630706",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur1",
              in: "SourceAlpha",
              result: "shadowBlurInner1",
              stdDeviation: "2.5"
            }),
            createBaseVNode("feOffset", {
              id: "feOffset1",
              dy: "1",
              in: "shadowBlurInner1",
              result: "shadowOffsetInner1"
            }),
            createBaseVNode("feComposite", {
              id: "feComposite1",
              in: "shadowOffsetInner1",
              in2: "SourceAlpha",
              k2: "-1",
              k3: "1",
              operator: "arithmetic",
              result: "shadowInnerInner1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix1",
              in: "shadowInnerInner1",
              values: "0 0 0 0 0.37254902 0 0 0 0 0.321568627 0 0 0 0 0.890196078 0 0 0 0.592848558 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "c",
            width: "1.3",
            height: "1.5125",
            x: "-0.15",
            y: "-0.2875",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur2",
              in: "SourceAlpha",
              result: "shadowBlurInner1",
              stdDeviation: "1.5"
            }),
            createBaseVNode("feOffset", {
              id: "feOffset2",
              dy: "-1",
              in: "shadowBlurInner1",
              result: "shadowOffsetInner1"
            }),
            createBaseVNode("feComposite", {
              id: "feComposite2",
              in: "shadowOffsetInner1",
              in2: "SourceAlpha",
              k2: "-1",
              k3: "1",
              operator: "arithmetic",
              result: "shadowInnerInner1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix2",
              in: "shadowInnerInner1",
              values: "0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "e",
            width: "1.1411765",
            height: "1.3107198",
            x: "-0.070588235",
            y: "-0.15535992",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feOffset", {
              id: "feOffset3",
              in: "SourceAlpha",
              result: "shadowOffsetOuter1"
            }),
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur3",
              in: "shadowOffsetOuter1",
              result: "shadowBlurOuter1",
              stdDeviation: ".5"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix3",
              in: "shadowBlurOuter1",
              values: "0 0 0 0 0.151865626 0 0 0 0 0 0 0 0 0 0.396201313 0 0 0 0.430670892 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "j",
            width: "1.3157895",
            height: "1.5384615",
            x: "-0.15789474",
            y: "-0.30769231",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur4",
              in: "SourceAlpha",
              result: "shadowBlurInner1",
              stdDeviation: "2.5"
            }),
            createBaseVNode("feOffset", {
              id: "feOffset4",
              dy: "-2",
              in: "shadowBlurInner1",
              result: "shadowOffsetInner1"
            }),
            createBaseVNode("feComposite", {
              id: "feComposite4",
              in: "shadowOffsetInner1",
              in2: "SourceAlpha",
              k2: "-1",
              k3: "1",
              operator: "arithmetic",
              result: "shadowInnerInner1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix4",
              in: "shadowInnerInner1",
              values: "0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "l",
            width: "1.1737557",
            height: "1.3924775",
            x: "-0.086877828",
            y: "-0.19623876",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feOffset", {
              id: "feOffset5",
              in: "SourceAlpha",
              result: "shadowOffsetOuter1"
            }),
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur5",
              in: "shadowOffsetOuter1",
              result: "shadowBlurOuter1",
              stdDeviation: "1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix5",
              in: "shadowBlurOuter1",
              values: "0 0 0 0 0.151865626 0 0 0 0 0 0 0 0 0 0.396201313 0 0 0 0.430670892 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "q",
            width: "1.3169811",
            height: "1.55",
            x: "-0.15849057",
            y: "-0.31666667",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur6",
              in: "SourceAlpha",
              result: "shadowBlurInner1",
              stdDeviation: "3.5"
            }),
            createBaseVNode("feOffset", {
              id: "feOffset6",
              dy: "-3",
              in: "shadowBlurInner1",
              result: "shadowOffsetInner1"
            }),
            createBaseVNode("feComposite", {
              id: "feComposite6",
              in: "shadowOffsetInner1",
              in2: "SourceAlpha",
              k2: "-1",
              k3: "1",
              operator: "arithmetic",
              result: "shadowInnerInner1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix6",
              in: "shadowInnerInner1",
              values: "0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "s",
            width: "1.1882353",
            height: "1.4220893",
            x: "-0.094117647",
            y: "-0.21104467",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feOffset", {
              id: "feOffset7",
              in: "SourceAlpha",
              result: "shadowOffsetOuter1"
            }),
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur7",
              in: "shadowOffsetOuter1",
              result: "shadowBlurOuter1",
              stdDeviation: "1.5"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix7",
              in: "shadowBlurOuter1",
              values: "0 0 0 0 0.151865626 0 0 0 0 0 0 0 0 0 0.396201313 0 0 0 0.430670892 0"
            })
          ]),
          createBaseVNode("filter", {
            id: "A",
            width: "1.8",
            height: "1.8208333",
            x: "-0.4",
            y: "-0.4",
            filterUnits: "objectBoundingBox"
          }, [
            createBaseVNode("feGaussianBlur", {
              id: "feGaussianBlur8",
              in: "SourceAlpha",
              result: "shadowBlurInner1",
              stdDeviation: "8"
            }),
            createBaseVNode("feOffset", {
              id: "feOffset8",
              dy: "1",
              in: "shadowBlurInner1",
              result: "shadowOffsetInner1"
            }),
            createBaseVNode("feComposite", {
              id: "feComposite8",
              in: "shadowOffsetInner1",
              in2: "SourceAlpha",
              k2: "-1",
              k3: "1",
              operator: "arithmetic",
              result: "shadowInnerInner1"
            }),
            createBaseVNode("feColorMatrix", {
              id: "feColorMatrix8",
              in: "shadowInnerInner1",
              values: "0 0 0 0 0.243686692 0 0 0 0 0.242040323 0 0 0 0 0.728091033 0 0 0 0.818673514 0"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "v",
            x1: "50%",
            x2: "50%",
            y1: "0%",
            y2: "100%"
          }, [
            createBaseVNode("stop", {
              id: "stop8",
              offset: "0%",
              "stop-color": "#29DA80"
            }),
            createBaseVNode("stop", {
              id: "stop9",
              offset: "100%",
              "stop-color": "#5F52E3"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "b",
            x1: "9.797959",
            x2: "9.797959",
            y1: "0",
            y2: "19.595918",
            gradientTransform: "scale(1.2247449,0.81649658)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop10",
              offset: "0%",
              "stop-color": "#E2FEFF"
            }),
            createBaseVNode("stop", {
              id: "stop11",
              offset: "100%",
              "stop-color": "#AE92FF"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "g",
            x1: "3.7187729",
            x2: "15.037714",
            y1: "11.71155",
            y2: "11.71155",
            gradientTransform: "scale(1.4835539,0.67405708)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop12",
              offset: "0%",
              "stop-color": "#29DA80"
            }),
            createBaseVNode("stop", {
              id: "stop13",
              offset: "100%",
              "stop-color": "#5F52E3"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "i",
            x1: "15.716234",
            x2: "15.716234",
            y1: "0",
            y2: "31.432467",
            gradientTransform: "scale(1.208941,0.82717019)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop14",
              offset: "0%",
              "stop-color": "#E2FEFF"
            }),
            createBaseVNode("stop", {
              id: "stop15",
              offset: "100%",
              "stop-color": "#AE92FF"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "n",
            x1: "5.5764515",
            x2: "23.732636",
            y1: "19.301545",
            y2: "19.301545",
            gradientTransform: "scale(1.5029271,0.66536827)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop16",
              offset: "0%",
              "stop-color": "#29DA80"
            }),
            createBaseVNode("stop", {
              id: "stop17",
              offset: "100%",
              "stop-color": "#5F52E3"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "p",
            x1: "21.84033",
            x2: "21.84033",
            y1: "0",
            y2: "43.680659",
            gradientTransform: "scale(1.2133516,0.82416338)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop18",
              offset: "0%",
              "stop-color": "#E2FEFF"
            }),
            createBaseVNode("stop", {
              id: "stop19",
              offset: "100%",
              "stop-color": "#AE92FF"
            })
          ]),
          createBaseVNode("linearGradient", {
            id: "u",
            x1: "7.8994411",
            x2: "33.130762",
            y1: "26.619224",
            y2: "26.619224",
            gradientTransform: "scale(1.4974477,0.66780295)",
            gradientUnits: "userSpaceOnUse"
          }, [
            createBaseVNode("stop", {
              id: "stop20",
              offset: "0%",
              "stop-color": "#29DA80"
            }),
            createBaseVNode("stop", {
              id: "stop21",
              offset: "100%",
              "stop-color": "#5F52E3"
            })
          ]),
          createBaseVNode("rect", {
            id: "a",
            width: "24",
            height: "16",
            x: "0",
            y: "0",
            rx: "5.455"
          }),
          createBaseVNode("rect", {
            id: "h",
            width: "38",
            height: "26",
            x: "0",
            y: "0",
            rx: "8.727"
          }),
          createBaseVNode("rect", {
            id: "o",
            width: "53",
            height: "36",
            x: "0",
            y: "0",
            rx: "12.218"
          }),
          createBaseVNode("path", {
            id: "f",
            d: "M5.517 4.383h17v7.724h-17z"
          }),
          createBaseVNode("path", {
            id: "m",
            d: "M8.381 7.283h27.625v12.23H8.381z"
          }),
          createBaseVNode("path", {
            id: "t",
            d: "M11.829 10.022h38.25V27.08h-38.25z"
          }),
          createBaseVNode("circle", {
            id: "z",
            cx: "198.183",
            cy: "585.665",
            r: "24"
          }),
          createBaseVNode("linearGradient", {
            id: "linearGradient44",
            "inkscape:collect": "always",
            "xlink:href": "#v",
            x1: "222.183",
            y1: "188.66499",
            x2: "222.183",
            y2: "262.66499",
            gradientUnits: "userSpaceOnUse",
            gradientTransform: "translate(0,-66.999993)"
          }),
          createBaseVNode("linearGradient", {
            id: "linearGradient45",
            "inkscape:collect": "always",
            "xlink:href": "#v",
            x1: "157.183",
            y1: "129.66499",
            x2: "157.183",
            y2: "203.66499",
            gradientUnits: "userSpaceOnUse",
            gradientTransform: "translate(0,-66.999993)"
          }),
          createBaseVNode("linearGradient", {
            id: "linearGradient46",
            "inkscape:collect": "always",
            "xlink:href": "#v",
            x1: "86.182999",
            y1: "89.665001",
            x2: "86.182999",
            y2: "163.665",
            gradientUnits: "userSpaceOnUse",
            gradientTransform: "translate(0,-67.000001)"
          })
        ], -1)),
        createBaseVNode("g", _hoisted_1$1, [
          _cache[7] || (_cache[7] = createBaseVNode("path", {
            id: "path21",
            d: "m 69.532,305 c -2.568,17.758 -1.657,52.335 39.76,59.182 41.42,6.847 53.527,-21.34 59.74,-45.452 26.951,-104.619 424.26,-104.94 449.394,0 7.29,30.434 25.498,54.596 64.957,45.452 C 722.842,355.038 721.196,318.73 717.96,305 700.311,165.825 686.86,23.179 686.86,23.179 L 250.713,0 17.692,73.305 0,243.667 c 49.721,17.615 72.898,38.06 69.532,61.333 z"
          }, null, -1)),
          _cache[8] || (_cache[8] = createBaseVNode("path", {
            id: "w",
            d: "m 76.664,8.056 c 12.12,-11.298 31.103,-10.631 42.4,1.488 11.298,12.12 10.631,31.103 -1.489,42.4 -13.319,12.416 -24.52,27.48 -33.634,45.352 -14.844,29.109 -22.744,56.946 -23.972,83.647 -0.761,16.551 -14.796,29.351 -31.347,28.59 -16.55,-0.76 -29.35,-14.795 -28.59,-31.346 C 1.671,142.559 11.914,106.464 30.491,70.037 42.723,46.051 58.124,25.337 76.664,8.056 Z"
          }, null, -1)),
          createBaseVNode("g", {
            id: "g26",
            opacity: unref(eraserSizeType) === unref(WhiteboardSizeEnum).SMALL ? 1 : 0.6,
            transform: "rotate(-20,195.5939,-176.5242)"
          }, _cache[3] || (_cache[3] = [
            createStaticVNode('<mask id="d" fill="#ffffff" data-v-deb144f7><use id="use21" xlink:href="#a" data-v-deb144f7></use></mask><use id="use22" xlink:href="#a" fill="url(#b)" fill-rule="evenodd" style="fill:url(#b);" data-v-deb144f7></use><use id="use23" xlink:href="#a" fill="#000000" filter="url(#c)" data-v-deb144f7></use><g id="g25" mask="url(#d)" transform="rotate(90,14.017,8.245)" data-v-deb144f7><use id="use24" xlink:href="#f" fill="#000000" filter="url(#e)" data-v-deb144f7></use><use id="use25" xlink:href="#f" fill="url(#g)" fill-rule="evenodd" style="fill:url(#g);" data-v-deb144f7></use></g>', 4)
          ]), 8, _hoisted_2$1),
          createBaseVNode("g", {
            id: "g31",
            opacity: unref(eraserSizeType) === unref(WhiteboardSizeEnum).MEDIUM ? 1 : 0.6,
            transform: "rotate(-20,333.84247,-335.50493)"
          }, _cache[4] || (_cache[4] = [
            createStaticVNode('<mask id="k" fill="#ffffff" data-v-deb144f7><use id="use26" xlink:href="#h" data-v-deb144f7></use></mask><use id="use27" xlink:href="#h" fill="url(#i)" fill-rule="evenodd" style="fill:url(#i);" data-v-deb144f7></use><use id="use28" xlink:href="#h" fill="#000000" filter="url(#j)" data-v-deb144f7></use><g id="g30" mask="url(#k)" transform="rotate(90,22.194,13.398)" data-v-deb144f7><use id="use29" xlink:href="#m" fill="#000000" filter="url(#l)" data-v-deb144f7></use><use id="use30" xlink:href="#m" fill="url(#n)" fill-rule="evenodd" style="fill:url(#n);" data-v-deb144f7></use></g>', 4)
          ]), 8, _hoisted_3$1),
          createBaseVNode("g", {
            id: "g36",
            opacity: unref(eraserSizeType) === unref(WhiteboardSizeEnum).LARGE ? 1 : 0.6,
            transform: "rotate(-20,528.63966,-464.13689)"
          }, _cache[5] || (_cache[5] = [
            createStaticVNode('<mask id="r" fill="#ffffff" data-v-deb144f7><use id="use31" xlink:href="#o" data-v-deb144f7></use></mask><use id="use32" xlink:href="#o" fill="url(#p)" fill-rule="evenodd" style="fill:url(#p);" data-v-deb144f7></use><use id="use33" xlink:href="#o" fill="#000000" filter="url(#q)" data-v-deb144f7></use><g id="g35" mask="url(#r)" transform="rotate(90,30.954,18.55)" data-v-deb144f7><use id="use34" xlink:href="#t" fill="#000000" filter="url(#s)" data-v-deb144f7></use><use id="use35" xlink:href="#t" fill="url(#u)" fill-rule="evenodd" style="fill:url(#u);" data-v-deb144f7></use></g>', 4)
          ]), 8, _hoisted_4),
          createBaseVNode("circle", {
            id: "circle36",
            cx: "222.183",
            cy: "158.66499",
            r: "37",
            fill: "transparent",
            onClick: _cache[0] || (_cache[0] = ($event) => onClickChangeEraserSize(unref(WhiteboardSizeEnum).LARGE))
          }),
          createBaseVNode("circle", {
            id: "circle37",
            cx: "157.183",
            cy: "99.665001",
            r: "37",
            fill: "transparent",
            onClick: _cache[1] || (_cache[1] = ($event) => onClickChangeEraserSize(unref(WhiteboardSizeEnum).MEDIUM))
          }),
          createBaseVNode("circle", {
            id: "circle38",
            cx: "86.182999",
            cy: "59.665001",
            r: "37",
            fill: "transparent",
            onClick: _cache[2] || (_cache[2] = ($event) => onClickChangeEraserSize(unref(WhiteboardSizeEnum).SMALL))
          }),
          unref(eraserSizeType) === unref(WhiteboardSizeEnum).LARGE ? (openBlock(), createElementBlock("circle", _hoisted_5)) : createCommentVNode("", true),
          unref(eraserSizeType) === unref(WhiteboardSizeEnum).MEDIUM ? (openBlock(), createElementBlock("circle", _hoisted_6)) : createCommentVNode("", true),
          unref(eraserSizeType) === unref(WhiteboardSizeEnum).SMALL ? (openBlock(), createElementBlock("circle", _hoisted_7)) : createCommentVNode("", true),
          _cache[9] || (_cache[9] = createStaticVNode('<g id="g42" opacity="0.25" transform="rotate(-144,169.2722,321.93901)" data-v-deb144f7><use id="use41" xlink:href="#w" fill="#ffffff" data-v-deb144f7></use><use id="use42" xlink:href="#w" fill="#000000" filter="url(#x)" data-v-deb144f7></use></g><g id="g43" transform="translate(39.141,510.339)" data-v-deb144f7><path id="y" d="m 0,101.044 c 0,0 98.84,5.12 174.171,-101.044" data-v-deb144f7></path><text id="text42" fill="#ffffff" fill-opacity="0.649" font-size="28px" letter-spacing="7.2" data-v-deb144f7><textPath id="textPath42" href="#y" startOffset="5.9999999%" data-v-deb144f7>滑动清屏</textPath></text></g>', 2)),
          createBaseVNode("g", {
            id: "g43",
            ref_key: "dragHandleRef",
            ref: dragHandleRef,
            transform: `translate(${unref(position).x / scalc}, ${unref(position).y / scalc})`
          }, _cache[6] || (_cache[6] = [
            createBaseVNode("use", {
              id: "use43",
              "xlink:href": "#z",
              fill: "#ffffff",
              "fill-rule": "evenodd"
            }, null, -1),
            createBaseVNode("use", {
              id: "use44",
              "xlink:href": "#z",
              fill: "#000000",
              filter: "url(#A)"
            }, null, -1),
            createBaseVNode("path", {
              id: "path44",
              stroke: "#ffffff",
              "stroke-linecap": "round",
              "stroke-width": "5",
              d: "m 187.44,599.024 c 0,0 10.205,8.408 23.807,0"
            }, null, -1)
          ]), 8, _hoisted_8)
        ])
      ], 512);
    };
  }
});
const WhiteboardTooldicsEraserSetting_vue_vue_type_style_index_0_scoped_deb144f7_lang = "";
const WhiteboardTooldicsEraserSetting = /* @__PURE__ */ _export_sfc$1(_sfc_main$2, [["__scopeId", "data-v-deb144f7"]]);
const _hoisted_1 = ["onClick"];
const _hoisted_2 = ["onClick"];
const _hoisted_3 = ["onClick"];
const dragThreshold = 100;
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "WhiteboardTooldics",
  emits: ["save-blackboard"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    const whiteboardStore = useWhiteboardStore();
    const {
      historyList,
      historyIndex,
      toolPopoverVisible,
      currentTool,
      currentToolIndex,
      currentSecondTool,
      isPencilTool,
      isEraserTool
    } = storeToRefs(whiteboardStore);
    const { scale } = storeToRefs(useThemeStore());
    const whiteboardTooldics = ref();
    const parentElement = useParentElement(whiteboardTooldics);
    let startPosition = {
      x: 0,
      y: 0
    };
    let isDragEvent = false;
    const calcBoundaryPosition = (position2) => {
      if (!parentElement.value || !whiteboardTooldics.value) {
        return null;
      }
      const containerRect = parentElement.value.getBoundingClientRect();
      const draggableRect = whiteboardTooldics.value.getBoundingClientRect();
      let newX = position2.x - containerRect.left;
      let newY = position2.y - containerRect.top;
      const expandWidth = isExpandTooldics.value ? 69 : 0;
      newX = Math.max(
        expandWidth * scale.value,
        Math.min(containerRect.width - draggableRect.width - scale.value * (79 + expandWidth), newX)
      );
      newY = Math.max(
        expandWidth * scale.value,
        Math.min(containerRect.height - draggableRect.height - scale.value * (62 + expandWidth), newY)
      );
      return {
        x: newX,
        y: newY
      };
    };
    const { style, position } = useDraggable(whiteboardTooldics, {
      capture: false,
      onStart(_, event) {
        stopAutoRetract();
        startPosition = {
          x: event.clientX,
          y: event.clientY
        };
        isDragEvent = false;
      },
      onMove(pointer) {
        if (Math.abs(pointer.x - startPosition.x) > dragThreshold || Math.abs(pointer.y - startPosition.y) > dragThreshold) {
          isDragEvent = true;
          isHasChildTools(currentTool.value) && whiteboardStore.hideToolPopover();
        }
        const res = calcBoundaryPosition(pointer);
        if (!res) {
          return;
        }
        position.value = res;
      },
      onEnd() {
        resetAutoRetract();
        isExpandTooldics.value && isHasChildTools(currentTool.value) && whiteboardStore.showToolPopover();
      },
      initialValue: {
        x: scale.value * 88,
        y: document.documentElement.clientHeight - scale.value * 227
      }
    });
    const isExpandTooldics = ref(true);
    let retractTimerIndex;
    const stopAutoRetract = () => {
      if (retractTimerIndex) {
        clearTimeout(retractTimerIndex);
        retractTimerIndex = null;
      }
    };
    const resetAutoRetract = () => {
      stopAutoRetract();
      retractTimerIndex = setTimeout(() => {
        if (isExpandTooldics.value) {
          isDragEvent = false;
          onClickAssistant();
        }
      }, 5e3);
    };
    const onClickAssistant = () => {
      if (isDragEvent) {
        return;
      }
      isExpandTooldics.value = !isExpandTooldics.value;
      if (isExpandTooldics.value === false) {
        whiteboardStore.hideToolPopover();
        stopAutoRetract();
      } else {
        const res = calcBoundaryPosition(position.value);
        if (res) {
          position.value = res;
        }
        if (isHasChildTools(currentTool.value)) {
          whiteboardStore.showToolPopover();
        }
        resetAutoRetract();
      }
    };
    const tools = [
      {
        iconClass: "xuanze1",
        name: "选择",
        isCanActive: true,
        handler() {
          whiteboardStore.setDrawStatus(false);
        }
      },
      {
        iconClass: "huabi-chushi",
        name: "画笔",
        isCanActive: true,
        isPencil: true,
        handler() {
          switch (whiteboardStore.brushType) {
            case IPencilModes.SOLID:
              whiteboardStore.switchDrawTool(MultiPencilPlugin.drawTool);
              break;
            default:
              whiteboardStore.switchDrawTool(PencilPlugin.drawTool);
              break;
          }
        }
      },
      {
        iconClass: "xiangpi",
        name: "橡皮",
        isCanActive: true,
        handler() {
          whiteboardStore.switchDrawTool(EraserPlugin.drawTool, {
            eraserSize: scale.value * DEFAULT_ERASER_HEIGHT
          });
        }
      },
      {
        iconClass: "graphic",
        name: "图形",
        isCanActive: true,
        handler() {
          whiteboardStore.switchDrawTool(SelectionPlugin.drawTool);
        },
        childClass: "graphic",
        childTools: [
          {
            iconClass: "line",
            name: "直线",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(LinePlugin.drawTool);
            }
          },
          {
            iconClass: "dashed",
            name: "虚线",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(LinePlugin.drawTool, {
                dashed: true
              });
            }
          },
          {
            iconClass: "arrow",
            name: "箭头",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(ArrowPlugin.drawTool);
            }
          },
          {
            iconClass: "circle",
            name: "圆形",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(CirclePlugin.drawTool);
            }
          },
          {
            iconClass: "ellipse",
            name: "椭圆",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(EllipsePlugin.drawTool);
            }
          },
          {
            iconClass: "square",
            name: "正方形",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(RectPlugin.drawTool, {
                isSquare: true
              });
            }
          },
          {
            iconClass: "rect",
            name: "矩形",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(RectPlugin.drawTool);
            }
          },
          {
            iconClass: "triangle",
            name: "三角形",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(TrianglePlugin.drawTool);
            }
          },
          {
            iconClass: "right-triangle",
            name: "直角三角形",
            isCanActive: true,
            handler() {
              whiteboardStore.switchDrawTool(RightTrianglePlugin.drawTool);
            }
          }
        ]
      },
      {
        iconClass: "baocun",
        name: "保存",
        handler() {
          emits("save-blackboard", position.value);
        }
      },
      {
        iconClass: "revoke",
        name: "撤销",
        handler() {
          const length = historyList.value.length;
          if (length > 0 && historyIndex.value < length - 1) {
            whiteboardStore.revoke();
          } else {
            logger.debug("无返回记录");
          }
        }
      }
    ];
    whiteboardStore.initTools(tools);
    const onClickToolItem = (tool, index) => {
      if (isDragEvent) {
        return;
      }
      whiteboardStore.activeTool(tool, index);
    };
    const onClickSecondToolItem = (tool, index) => {
      whiteboardStore.activeSecondTool(tool, index);
    };
    __expose({
      setPosition(pointer) {
        position.value = calcBoundaryPosition(pointer) || pointer;
      }
    });
    return (_ctx, _cache) => {
      const _component_SvgIcon = SvgIcon;
      const _component_el_popover = ElPopover;
      const _directive_stop_drag = resolveDirective("stop-drag");
      return openBlock(), createElementBlock("div", {
        ref_key: "whiteboardTooldics",
        ref: whiteboardTooldics,
        class: "whiteboard-tooldics",
        style: normalizeStyle(unref(style))
      }, [
        isExpandTooldics.value && unref(isPencilTool) ? withDirectives((openBlock(), createBlock(WhiteboardTooldicsPencilSetting, {
          key: 0,
          class: "whiteboard-pencil-edit",
          onPointerdown: resetAutoRetract,
          onShowColorPicker: stopAutoRetract,
          onHideColorPicker: resetAutoRetract
        }, null, 512)), [
          [_directive_stop_drag]
        ]) : createCommentVNode("", true),
        isExpandTooldics.value && unref(isEraserTool) ? withDirectives((openBlock(), createBlock(WhiteboardTooldicsEraserSetting, {
          key: 1,
          class: "whiteboard-eraser-edit",
          onPointerdown: resetAutoRetract
        }, null, 512)), [
          [_directive_stop_drag]
        ]) : createCommentVNode("", true),
        createVNode(_component_el_popover, {
          visible: unref(toolPopoverVisible),
          "show-arrow": false,
          teleported: false,
          width: "auto",
          placement: "right",
          "popper-class": "whiteboard-tooldics-popper",
          transition: "no-animation"
        }, createSlots({
          reference: withCtx(() => [
            createBaseVNode("div", {
              class: normalizeClass(["whiteboard-tooldics-wrap", {
                "not-expand": !isExpandTooldics.value
              }])
            }, [
              _cache[0] || (_cache[0] = createBaseVNode("div", { class: "whiteboard-tooldics-star-bg" }, null, -1)),
              unref(currentToolIndex) !== null && unref(currentTool)?.isCanActive ? (openBlock(), createElementBlock("div", {
                key: 0,
                class: normalizeClass(["whiteboard-tooldics-active", {
                  "not-expand": !isExpandTooldics.value
                }]),
                style: normalizeStyle({
                  transform: `rotate(${(unref(currentToolIndex) || 0) * 60}deg)`
                })
              }, null, 6)) : createCommentVNode("", true),
              (openBlock(), createElementBlock(Fragment, null, renderList(tools, (tool, index) => {
                return createBaseVNode("div", {
                  key: String(tool.name),
                  class: normalizeClass(["whiteboard-tooldics-item flex flex-jc flex-ac", `tooldics-item-${index + 1}`]),
                  onClick: ($event) => onClickToolItem(tool, index)
                }, [
                  tool.isPencil ? (openBlock(), createBlock(WhiteboardTooldicsPencilIcon, {
                    key: 0,
                    class: "whiteboard-tooldics-icon"
                  })) : (openBlock(), createBlock(_component_SvgIcon, {
                    key: 1,
                    class: "whiteboard-tooldics-icon",
                    "icon-class": tool.iconClass
                  }, null, 8, ["icon-class"]))
                ], 10, _hoisted_1);
              }), 64))
            ], 2)
          ]),
          _: 2
        }, [
          unref(currentTool)?.childTools?.length ? {
            name: "default",
            fn: withCtx(() => [
              withDirectives((openBlock(), createElementBlock("div", {
                class: "tooldics-popper-title",
                onPointerdown: resetAutoRetract
              }, [
                createTextVNode(toDisplayString(unref(currentTool).name), 1)
              ], 32)), [
                [_directive_stop_drag]
              ]),
              unref(currentTool).childTools ? withDirectives((openBlock(), createElementBlock("div", {
                key: 0,
                class: normalizeClass(["tooldics-popper-list flex", `tooldics-popper-${unref(currentTool).childClass}`]),
                onPointerdown: resetAutoRetract
              }, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(currentTool).childTools.slice(0, 3), (childTool, index) => {
                  return openBlock(), createElementBlock("div", {
                    key: String(childTool.name),
                    class: normalizeClass(["tooldics-popper-item flex flex-v flex-ac", {
                      active: unref(currentSecondTool) === childTool
                    }]),
                    onClick: ($event) => onClickSecondToolItem(childTool, index)
                  }, [
                    createVNode(_component_SvgIcon, {
                      class: "tooldics-popper-icon",
                      "icon-class": childTool.iconClass
                    }, null, 8, ["icon-class"])
                  ], 10, _hoisted_2);
                }), 128))
              ], 34)), [
                [_directive_stop_drag]
              ]) : createCommentVNode("", true),
              unref(currentTool).childTools ? withDirectives((openBlock(), createElementBlock("div", {
                key: 1,
                class: normalizeClass(["tooldics-popper-list flex", `tooldics-popper-${unref(currentTool).childClass}`])
              }, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(currentTool).childTools.slice(3), (childTool, index) => {
                  return openBlock(), createElementBlock("div", {
                    key: String(childTool.name),
                    class: normalizeClass(["tooldics-popper-item flex flex-v flex-ac", {
                      active: unref(currentSecondTool) === childTool
                    }]),
                    onClick: ($event) => onClickSecondToolItem(childTool, index)
                  }, [
                    createVNode(_component_SvgIcon, {
                      class: "tooldics-popper-icon",
                      "icon-class": childTool.iconClass
                    }, null, 8, ["icon-class"])
                  ], 10, _hoisted_3);
                }), 128))
              ], 2)), [
                [_directive_stop_drag]
              ]) : createCommentVNode("", true)
            ]),
            key: "0"
          } : void 0
        ]), 1032, ["visible"]),
        createVNode(WhiteboardTooldicsCenter, {
          "is-expand": isExpandTooldics.value,
          "current-tool": unref(currentTool),
          onPointerup: onClickAssistant
        }, null, 8, ["is-expand", "current-tool"])
      ], 4);
    };
  }
});
const WhiteboardTooldics_vue_vue_type_style_index_0_scoped_170fae8a_lang = "";
const WhiteboardTooldics_vue_vue_type_style_index_1_lang = "";
const WhiteboardTooldics = /* @__PURE__ */ _export_sfc$1(_sfc_main$1, [["__scopeId", "data-v-170fae8a"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Test",
  setup(__props) {
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(Fragment, null, [
        createVNode(Whiteboard, { class: "whiteboard" }),
        createVNode(WhiteboardTooldics)
      ], 64);
    };
  }
});
const Test_vue_vue_type_style_index_0_scoped_6fab7239_lang = "";
const Test = /* @__PURE__ */ _export_sfc$1(_sfc_main, [["__scopeId", "data-v-6fab7239"]]);
export {
  Test as default
};
