import { _ as _export_sfc, f as px2rem, B as BridgeZmqUtils, c as setBusinessInfoWidget, l as logger, d as initLoggerWidget, p as pinia } from "./index.9b9bd033.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { r as ref, z as onMounted, b as openBlock, m as createElementBlock, j as createBaseVNode, F as Fragment, R as renderList, k as normalizeClass, H as toDisplayString, u as unref, l as normalizeStyle, d as defineComponent, c as computed, w as watch, n as nextTick, p as createVNode, h as withDirectives, Q as resolveDirective, ad as createApp } from "./bootstrap.ab073eb8.js";
import { d as getClassStudentList } from "./school.662f4ee7.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import { s as stopDrag } from "./stopDrag.9e5623d9.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./axios.b90ffefd.js";
import "./toast.4a39bbb3.js";
import "./index.5cc837e0.js";
import "./crypto-js.7319a219.js";
import "./toastWidget.501568b8.js";
const _imports_0 = "data:image/webp;base64,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";
const CountSwiper_vue_vue_type_style_index_0_scoped_41ff8d38_lang = "";
const _sfc_main$2 = {
  __name: "CountSwiper",
  emits: ["change"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const numbers = Array.from({ length: 5 }, (_, i) => i + 1);
    const selectedIndex = ref(0);
    const wrapperRef = ref(null);
    const trackRef = ref(null);
    const itemRef = ref(null);
    const itemWidth = ref(0);
    const translateX = ref(0);
    let startX = 0;
    let currentX = 0;
    let dragging = false;
    const onDragStart = (e) => {
      dragging = true;
      startX = e.touches?.[0]?.clientX ?? e.clientX;
      document.addEventListener("mousemove", onDragMove);
      document.addEventListener("mouseup", onDragEnd);
      document.addEventListener("touchmove", onDragMove);
      document.addEventListener("touchend", onDragEnd);
      e.cancelBubble = true;
    };
    const onDragMove = (e) => {
      if (!dragging)
        return;
      currentX = e.touches?.[0]?.clientX ?? e.clientX;
      const delta = currentX - startX;
      translateX.value += delta;
      startX = currentX;
    };
    const updateTranslateX = () => {
      const wrapperWidth = wrapperRef.value?.offsetWidth || 0;
      translateX.value = wrapperWidth / 2 - itemWidth.value / 2 - selectedIndex.value * itemWidth.value;
    };
    const onDragEnd = () => {
      dragging = false;
      const wrapperWidth = wrapperRef.value?.offsetWidth || 0;
      const totalShift = -(translateX.value - (wrapperWidth / 2 - itemWidth.value / 2));
      const centerIndex = Math.round(totalShift / itemWidth.value);
      selectedIndex.value = Math.max(0, Math.min(numbers.length - 1, centerIndex));
      updateTranslateX();
      emit("change", selectedIndex.value);
      document.removeEventListener("mousemove", onDragMove);
      document.removeEventListener("mouseup", onDragEnd);
      document.removeEventListener("touchmove", onDragMove);
      document.removeEventListener("touchend", onDragEnd);
    };
    onMounted(() => {
      if (itemRef.value && itemRef.value[0]) {
        itemWidth.value = itemRef.value[0].offsetWidth;
      }
      updateTranslateX();
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        ref_key: "wrapperRef",
        ref: wrapperRef,
        class: "number-selector",
        onMousedown: onDragStart,
        onTouchstart: onDragStart
      }, [
        createBaseVNode("div", {
          ref_key: "trackRef",
          ref: trackRef,
          class: "number-track",
          style: normalizeStyle({ transform: `translateX(${translateX.value}px)` })
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(numbers), (n, i) => {
            return openBlock(), createElementBlock("div", {
              key: i,
              ref_for: true,
              ref_key: "itemRef",
              ref: itemRef,
              class: normalizeClass(["number-item", { active: i === selectedIndex.value }])
            }, toDisplayString(n), 3);
          }), 128))
        ], 4)
      ], 544);
    };
  }
};
const CountSwiper = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-41ff8d38"]]);
const _hoisted_1$1 = { class: "container" };
const _hoisted_2$1 = { class: "roller-wrapper" };
const _hoisted_3$1 = {
  key: 1,
  class: "roller-stage-waiting"
};
const _hoisted_4$1 = {
  key: 2,
  class: "roller-stage-waiting empty"
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "RollContainer",
  props: {
    winnerCount: { default: 1 },
    names: { default: () => [] }
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const renderNames = computed(() => {
      if (!rollerStyle.value)
        return [];
      const nameList = props.names.map((name) => {
        const isEnglish = isEnglishName(name);
        const units = isEnglish ? name.split(" ") : name.split("");
        if (isEnglish) {
          if (measureTextWidth(units) <= maxLineWidth.value)
            return { units, isEnglish };
          let maxPrefix = 0;
          for (let i2 = 0; i2 < units.length; i2++) {
            const test = [...units.slice(0, i2 + 1), "..."];
            if (measureTextWidth(test) <= maxLineWidth.value) {
              maxPrefix = i2 + 1;
            } else {
              break;
            }
          }
          const testTail = ["...", ...units.slice(-1)];
          const tailWidth = measureTextWidth(testTail);
          if (tailWidth > maxLineWidth.value) {
            return { units: [...units.slice(0, maxPrefix), "..."], isEnglish };
          }
          let i = 0, j = units.length - 1;
          let result = ["..."];
          while (i < j) {
            const test = [...units.slice(0, i + 1), "...", ...units.slice(j)];
            if (measureTextWidth(test) <= maxLineWidth.value) {
              result = test;
              i++;
              j--;
            } else {
              break;
            }
          }
          if (result.length === 1 && result[0] === "...") {
            result = [units[0], "..."];
          }
          return { units: result, isEnglish };
        } else {
          if (units.length > 5) {
            return {
              units: [units[0], units[1], "...", units[units.length - 2], units[units.length - 1]],
              isEnglish
            };
          }
          return { units, isEnglish };
        }
      });
      return [...nameList, ...nameList, ...nameList];
    });
    const measureTextWidth = (words) => {
      const span = document.createElement("span");
      span.style.visibility = "hidden";
      span.style.position = "absolute";
      span.style.whiteSpace = "nowrap";
      span.style.fontFamily = "inherit";
      span.style.fontWeight = "bold";
      span.style.fontSize = "0.825em";
      span.innerText = words.join(" ");
      rollContainerRef.value?.appendChild(span);
      const width = span.getBoundingClientRect().width;
      rollContainerRef.value?.removeChild(span);
      return width;
    };
    function isEnglishName(name) {
      const hasChinese = /[\u4e00-\u9fa5]/.test(name);
      if (hasChinese)
        return false;
      return /^[a-zA-Z\s.'-]+$/.test(name);
    }
    const rollContainerRef = ref(null);
    const maxLineWidth = ref(0);
    const stage = ref("waiting");
    const currentTranslateY = ref(0);
    const speed = ref(0);
    const isRolling = ref(false);
    const winners = ref([]);
    const winnersIndex = ref([]);
    const letterOffsets = ref([]);
    const letterRolling = ref([]);
    let animationId = null;
    let letterAnimationId = null;
    const fontSizeList = [26, 21, 21, 18, 13];
    const rollerStyle = computed(() => {
      const count = Math.min(props.winnerCount, 5, props.names.length);
      return {
        height: `${px2rem(count * itemHeight.value + count + 1)}rem`,
        fontSize: `${px2rem(fontSizeList[count - 1])}rem`
      };
    });
    const lineHeightList = [51, 35, 30, 28, 22];
    const itemHeight = computed(() => {
      const size = Math.min(props.winnerCount, 5, props.names.length);
      return lineHeightList[size - 1];
    });
    const letterStyle = computed(() => {
      return {
        height: `${px2rem(itemHeight.value)}rem`,
        lineHeight: `${px2rem(itemHeight.value)}rem`
      };
    });
    const letterRollerStyle = computed(() => {
      return {
        overflow: stage.value === "rolling-names" ? "visible" : "hidden",
        height: `${px2rem(itemHeight.value)}rem`
      };
    });
    watch(
      () => props.winnerCount,
      () => {
        stage.value = "waiting";
      }
    );
    const getBlur = (stage2, isRolling2) => {
      if (stage2 === "rolling-names") {
        const blurValue = Math.min(speed.value / 5, 5);
        return `blur(${blurValue}px)`;
      }
      if (stage2 === "rolling-letters" && isRolling2) {
        return "blur(3px)";
      }
      return "blur(0px)";
    };
    const startRolling = () => {
      if (isRolling.value)
        return;
      props.names.sort(() => 0.5 - Math.random());
      isRolling.value = true;
      stage.value = "rolling-names";
      currentTranslateY.value = 0;
      speed.value = 30;
      rollNames();
      setTimeout(() => {
        startStopping();
      }, 1e3);
    };
    const rollNames = () => {
      if (!isRolling.value)
        return;
      currentTranslateY.value -= px2rem(speed.value);
      const totalHeight = px2rem(renderNames.value.length * itemHeight.value);
      if (Math.abs(currentTranslateY.value) >= totalHeight / 3) {
        currentTranslateY.value = 0;
      }
      animationId = requestAnimationFrame(rollNames);
    };
    const startStopping = () => {
      cancelAnimationFrame(animationId);
      animationId = null;
      const total = Math.min(props.winnerCount, 5, props.names.length);
      winners.value = [];
      winnersIndex.value = [];
      const picked = /* @__PURE__ */ new Set();
      let idx = Math.floor(Math.random() * (props.names.length - total));
      while (winners.value.length < total) {
        if (!picked.has(idx)) {
          winners.value.push(props.names[idx]);
          winnersIndex.value.push(idx);
          picked.add(idx);
        }
        idx++;
      }
      const namesPerRound = props.names.length;
      const currentRound = Math.floor(
        Math.abs(currentTranslateY.value) / (itemHeight.value * namesPerRound)
      );
      const firstWinner = winnersIndex.value[0];
      currentTranslateY.value = -px2rem(
        (currentRound * namesPerRound + firstWinner) * (itemHeight.value + 1) + 1
      );
      isRolling.value = false;
      startLetterRolling();
    };
    const startLetterRolling = () => {
      stage.value = "rolling-letters";
      letterOffsets.value = winners.value.map((_) => 0);
      letterRolling.value = winners.value.map((_) => true);
      rollLetters();
      setTimeout(() => {
        stopLettersOneByOne();
      });
    };
    const rollLetters = () => {
      if (!letterRolling.value.some((v) => v))
        return;
      letterOffsets.value = letterOffsets.value.map(
        (offset, idx) => letterRolling.value[idx] ? offset + 10 : offset
      );
      letterAnimationId = requestAnimationFrame(rollLetters);
    };
    const stopLettersOneByOne = () => {
      winners.value.forEach((_, winnerIdx) => {
        setTimeout(() => {
          stopLetter(winnerIdx);
        }, winnerIdx * 200);
      });
      setTimeout(
        () => {
          if (letterAnimationId)
            cancelAnimationFrame(letterAnimationId);
        },
        winners.value.reduce((sum, n) => sum + n.length, 0) * 100 + 300
      );
    };
    const stopLetter = (winnerIdx) => {
      letterRolling.value[winnerIdx] = false;
      letterOffsets.value[winnerIdx] = 0;
    };
    const getLetterOffset = (lineIdx) => {
      if (stage.value === "rolling-names")
        return 0;
      const winnerLine = lineIdx % props.names.length;
      if (winnersIndex.value.includes(winnerLine)) {
        return px2rem(letterOffsets.value[winnersIndex.value.indexOf(winnerLine)] || 0);
      }
      return 0;
    };
    const isLetterRolling = (lineIdx) => {
      if (stage.value === "rolling-names")
        return true;
      const winnerLine = lineIdx % props.names.length;
      if (winnersIndex.value.includes(winnerLine)) {
        return letterRolling.value[winnersIndex.value.indexOf(winnerLine)] ?? false;
      }
      return false;
    };
    onMounted(() => {
      nextTick(() => {
        if (rollContainerRef.value) {
          const container = rollContainerRef.value;
          const computedStyle = window.getComputedStyle(container);
          const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
          const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
          maxLineWidth.value = container.clientWidth - paddingLeft - paddingRight;
          console.log("maxLineWidth", maxLineWidth.value);
        }
      });
    });
    __expose({
      startRolling
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$1, [
        createBaseVNode("div", _hoisted_2$1, [
          createBaseVNode("div", {
            ref_key: "rollContainerRef",
            ref: rollContainerRef,
            class: "roller",
            style: normalizeStyle(rollerStyle.value)
          }, [
            _ctx.names.length && stage.value !== "waiting" ? (openBlock(), createElementBlock("div", {
              key: 0,
              class: "roller-inner",
              style: normalizeStyle({
                transform: `translateY(${currentTranslateY.value}rem)`,
                transition: "none"
              })
            }, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(renderNames.value, (name, idx) => {
                return openBlock(), createElementBlock("div", {
                  key: idx,
                  style: normalizeStyle({
                    "border-bottom-width": stage.value === "rolling-names" ? "0" : "1px"
                  }),
                  class: normalizeClass(["name-line", { "is-english": name.isEnglish }])
                }, [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(name.units, (char, cidx) => {
                    return openBlock(), createElementBlock("div", {
                      key: cidx,
                      style: normalizeStyle(letterRollerStyle.value),
                      class: "letter-roller"
                    }, [
                      createBaseVNode("div", {
                        class: "roller-inner-letter",
                        style: normalizeStyle({
                          transform: stage.value === "rolling-names" ? "translateY(0px)" : `translateY(-${getLetterOffset(idx)}rem)`,
                          transition: stage.value === "rolling-names" ? "none" : isLetterRolling(idx) ? "none" : "transform 0.3s ease-out",
                          filter: getBlur(stage.value, isLetterRolling(idx))
                        })
                      }, [
                        createBaseVNode("div", {
                          class: "letter-item",
                          style: normalizeStyle(letterStyle.value)
                        }, toDisplayString(char), 5)
                      ], 4)
                    ], 4);
                  }), 128))
                ], 6);
              }), 128))
            ], 4)) : _ctx.names.length ? (openBlock(), createElementBlock("div", _hoisted_3$1, "等待抽选")) : (openBlock(), createElementBlock("div", _hoisted_4$1, "班级无学生名单"))
          ], 4)
        ])
      ]);
    };
  }
});
const RollContainer_vue_vue_type_style_index_0_scoped_a46ed07d_lang = "";
const RollContainer = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-a46ed07d"]]);
const _hoisted_1 = { class: "drag-container" };
const _hoisted_2 = { class: "roll-call-panel" };
const _hoisted_3 = { class: "roll-count-box flex" };
const _hoisted_4 = { class: "roll-name-box flex flex-ac flex-jc" };
const _hoisted_5 = { class: "roll-button" };
const _hoisted_6 = { class: "roll-pole-box" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const rollContainerRef = ref();
    const winnerCount = ref(1);
    const handleCountChange = (count) => {
      winnerCount.value = count + 1;
    };
    const ballClass = ref("");
    const poleClass = ref("");
    let animating = false;
    const triggerRollAnimation = () => {
      if (!studentList.value.length)
        return;
      if (animating)
        return;
      rollContainerRef.value?.startRolling();
      animating = true;
      ballClass.value = "move-down";
      poleClass.value = "shorten";
      setTimeout(() => {
        ballClass.value = "move-up";
        poleClass.value = "elongate";
      }, 400);
      setTimeout(() => {
        ballClass.value = "";
        poleClass.value = "";
        animating = false;
      }, 800);
    };
    const studentList = ref([]);
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
      event.target && event.target.dispatchEvent(new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
        clientX: event.clientX,
        clientY: event.clientY
      }));
    });
    onMounted(async () => {
      try {
        const res = await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS);
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
        const params = {
          saasSchoolId: classTeacher.saasSchoolId,
          saasClassId: classTeacher.saasClassId
        };
        const { data } = await getClassStudentList(params, { isBridgeQtShow: true });
        if (Array.isArray(data.classStudentList)) {
          studentList.value = data.classStudentList.map((item) => item.name);
        } else {
          logger.warn("【随机点名】", "获取学生列表数据结构错误", data);
        }
      } catch (e) {
        logger.error("【随机点名】", "初始化失败", e);
      }
    });
    return (_ctx, _cache) => {
      const _directive_stop_drag = resolveDirective("stop-drag");
      const _directive_pressed_class = resolveDirective("pressed-class");
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createBaseVNode("div", _hoisted_2, [
          createBaseVNode("div", _hoisted_3, [
            _cache[0] || (_cache[0] = createBaseVNode("span", null, "抽取", -1)),
            createVNode(CountSwiper, { onChange: handleCountChange }),
            _cache[1] || (_cache[1] = createBaseVNode("span", null, "人", -1))
          ]),
          createBaseVNode("div", _hoisted_4, [
            createVNode(RollContainer, {
              ref_key: "rollContainerRef",
              ref: rollContainerRef,
              "winner-count": winnerCount.value,
              names: studentList.value
            }, null, 8, ["winner-count", "names"])
          ]),
          createBaseVNode("div", _hoisted_5, [
            withDirectives(createBaseVNode("img", {
              class: "pressed-scale hl-sls-track",
              src: _imports_0,
              alt: "roll",
              "data-text": "开始抽取",
              "data-module": "星讲台-随机点名",
              onClick: triggerRollAnimation
            }, null, 512), [
              [_directive_stop_drag],
              [_directive_pressed_class]
            ])
          ])
        ]),
        createBaseVNode("div", _hoisted_6, [
          createBaseVNode("div", {
            class: normalizeClass(["ball", ballClass.value])
          }, null, 2),
          createBaseVNode("div", {
            class: normalizeClass(["pole", poleClass.value])
          }, null, 2)
        ])
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_0e676ea3_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-0e676ea3"]]);
const onPointerdown = (e) => {
  const target = e.currentTarget;
  if (target instanceof HTMLElement) {
    target.classList.add("pressed");
  }
};
const onPointerup = (e) => {
  const target = e.currentTarget;
  if (target instanceof HTMLElement) {
    target.classList.remove("pressed");
  }
};
const onPointerCancel = (e) => {
  const target = e.currentTarget;
  if (target instanceof HTMLElement) {
    target.classList.remove("pressed");
  }
};
const pressedClass = {
  mounted(el) {
    el.addEventListener("pointerdown", onPointerdown);
    el.addEventListener("pointerup", onPointerup);
    el.addEventListener("pointercancel", onPointerCancel);
  },
  beforeUnmount(el) {
    el.removeEventListener("pointerdown", onPointerdown);
    el.removeEventListener("pointerup", onPointerup);
    el.removeEventListener("pointercancel", onPointerCancel);
  }
};
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.directive("stop-drag", stopDrag);
app.directive("pressed-class", pressedClass);
app.use(pinia).mount("#app");
