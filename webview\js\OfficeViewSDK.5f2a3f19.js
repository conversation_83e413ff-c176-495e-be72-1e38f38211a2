import { l as logger, v as v4 } from "./index.9b9bd033.js";
import { E as EventEmitter } from "./hlwhiteboard.b54f17ff.js";
var COMMUNICATE_MESSAGE_TYPE = /* @__PURE__ */ ((COMMUNICATE_MESSAGE_TYPE2) => {
  COMMUNICATE_MESSAGE_TYPE2["INVOKE"] = "invoke";
  COMMUNICATE_MESSAGE_TYPE2["RESPONSE"] = "response";
  COMMUNICATE_MESSAGE_TYPE2["DOMContentLoaded"] = "DOMContentLoaded";
  COMMUNICATE_MESSAGE_TYPE2["ON_LOAD_EVENT"] = "onloadEvent";
  COMMUNICATE_MESSAGE_TYPE2["PREV"] = "prev";
  COMMUNICATE_MESSAGE_TYPE2["NEXT"] = "next";
  COMMUNICATE_MESSAGE_TYPE2["PREV_SLIDE"] = "prevSlide";
  COMMUNICATE_MESSAGE_TYPE2["NEXT_SLIDE"] = "nextSlide";
  COMMUNICATE_MESSAGE_TYPE2["JUMP_TO"] = "jumpTo";
  COMMUNICATE_MESSAGE_TYPE2["GET_CONTENT_DETAILS"] = "getContentDetails";
  COMMUNICATE_MESSAGE_TYPE2["GET_CURRENT_STATUS"] = "getCurrentStatus";
  COMMUNICATE_MESSAGE_TYPE2["HAS_PREV"] = "hasPrev";
  COMMUNICATE_MESSAGE_TYPE2["HAS_NEXT"] = "hasNext";
  COMMUNICATE_MESSAGE_TYPE2["GO_PAGE_EVENT"] = "gopageEvent";
  COMMUNICATE_MESSAGE_TYPE2["PAGE_CHANGED_EVENT"] = "pageChangedEvent";
  return COMMUNICATE_MESSAGE_TYPE2;
})(COMMUNICATE_MESSAGE_TYPE || {});
class OfficeViewSDK extends EventEmitter {
  constructor(iframeDom) {
    super();
    this.iframeDom = null;
    this.handleMessage = (data) => {
      const { type, reqId } = data;
      console.log("【OfficeViewSDK】响应", reqId, data);
      switch (type) {
        case "response":
          this.emit(reqId, data);
          break;
        default:
          this.emit(type, data);
          break;
      }
    };
    this.iframeDom = iframeDom;
    this.init();
  }
  /**
   * 生成office预览地址
   * @param id 文档id
   * @param documentHtmlUrl 文档地址
   * @param slide 幻灯片索引
   * @param step 帧
   */
  static genOfficeViewSrc(id, documentHtmlUrl, slide = 0, step = 0) {
    try {
      const url = new URL(documentHtmlUrl);
      url.searchParams.set("documentId", id);
      url.searchParams.set("random", String(Date.now()));
      url.searchParams.set("slide", String(slide < 0 ? 0 : slide));
      url.searchParams.set("step", String(step < 0 ? 0 : step));
      url.searchParams.set("hideToolbar", "1");
      url.searchParams.set("supportGesture", "1");
      url.searchParams.set("r", String(Date.now()));
      return url.toString();
    } catch (error) {
      logger.error("【classroom】地址生成失败", error, documentHtmlUrl);
      return "";
    }
  }
  /**
   * 获取缩略图地址
   */
  static getThumbnailUrl(doc) {
    if (!doc)
      return "";
    const { transThirdName, thumbnailUrl } = doc;
    if (!thumbnailUrl) {
      logger.warn("【classroom】无缩略图地址", doc);
      return "";
    }
    switch (transThirdName) {
      case "WY":
        return thumbnailUrl;
      case "WYS":
        return thumbnailUrl?.replace(/(thum\/$)/, "pic/") || "";
    }
    return thumbnailUrl;
  }
  init() {
    window.addEventListener(
      "message",
      (event) => {
        if (event.origin !== "https://jzjx-test-resource.hailiangedu.com") {
          return;
        }
        this.handleMessage(event.data);
      },
      false
    );
  }
  /**
   * 切换 iframe dom
   * @param iframeDom iframe dom
   */
  switchIframeDom(iframeDom) {
    this.iframeDom = iframeDom;
  }
  /**
   * 调用方法
   * @param method 调用方法
   * @param params 参数
   * @param timeout 超时实际
   */
  invoke(method, params = {}, timeout = 3e3) {
    const reqId = this.postMessageToIframe("invoke", {
      method,
      ...params
    });
    if (!reqId) {
      Promise.reject(new Error("消息发送失败"));
      return;
    }
    return this.onceAsync(reqId, timeout);
  }
  /**
   * 发送消息给 iframe
   * @param type 类型
   * @param data 数据
   * @returns
   */
  postMessageToIframe(type, data = {}) {
    let params = {};
    try {
      if (!this.iframeDom) {
        return null;
      }
      const reqId = v4();
      params = {
        type,
        ...data,
        reqId
      };
      logger.debug("【OfficeViewSDK】发送", params);
      this.iframeDom?.contentWindow?.postMessage(params, "*");
      return reqId;
    } catch (error) {
      logger.warn("【OfficeViewSDK】发送失败", error, params);
      return null;
    }
  }
  /**
   * 包装成promise
   * @param eventFn 事件方法
   * @param type 事件类型
   * @param timeout 超时事件
   */
  _warpPromise(eventFn, type, timeout = 3e3) {
    return new Promise((resolve, reject) => {
      const fn = (res) => {
        resolve(res);
        clearIndex && clearTimeout(clearIndex);
      };
      eventFn(type, fn);
      const clearIndex = setTimeout(() => {
        reject(new Error("请求超时"));
        this.off(type, fn);
      }, timeout);
    });
  }
  onceAsync(type, timeout = 3e3) {
    return this._warpPromise(this.once.bind(this), type, timeout);
  }
  onAsync(type, timeout = 3e3) {
    return this._warpPromise(this.on.bind(this), type, timeout);
  }
  /**
   * 销毁方法
   */
  destroy() {
    window.removeEventListener("message", this.handleMessage);
    this.removeAllListeners();
    this.iframeDom = null;
  }
}
export {
  COMMUNICATE_MESSAGE_TYPE as C,
  OfficeViewSDK as O
};
