import { R as ResourceTypeEnum } from "./IResource.516d6004.js";
import { v as v4 } from "./index.9b9bd033.js";
function getFileType(fileName) {
  if (typeof fileName !== "string") {
    return ResourceTypeEnum.UNKNOWN;
  }
  const suffix = fileName.split(".").pop()?.toLowerCase();
  if (suffix === void 0) {
    return ResourceTypeEnum.UNKNOWN;
  }
  if (suffix === "pdf") {
    return ResourceTypeEnum.PDF;
  } else if (["ppt", "pptx"].includes(suffix)) {
    return ResourceTypeEnum.PPT;
  } else if (["jpg", "jpeg", "png", "gif", "bmp"].includes(suffix)) {
    return ResourceTypeEnum.IMAGE;
  } else if (["mp4", "avi", "wmv", "mov", "flv", "rmvb"].includes(suffix)) {
    return ResourceTypeEnum.VIDEO;
  } else if (["mp3", "wav", "wma"].includes(suffix)) {
    return ResourceTypeEnum.AUDIO;
  } else {
    return ResourceTypeEnum.UNKNOWN;
  }
}
function getFileNameWithoutExtension(fileName) {
  if (typeof fileName !== "string" || fileName === null || fileName === void 0) {
    return fileName;
  }
  if (fileName === "" || fileName === ".") {
    return fileName;
  }
  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1) {
    return fileName;
  }
  if (lastDotIndex === fileName.length - 1) {
    return fileName;
  }
  return fileName.substring(0, lastDotIndex);
}
function getFileSuffix(filename) {
  const pos = filename.lastIndexOf(".");
  let suffix = "";
  if (pos !== -1) {
    suffix = filename.substring(pos + 1);
  }
  return suffix;
}
function genUniqueFilename(filename) {
  return `${(/* @__PURE__ */ new Date()).getTime()}-${v4()}.${getFileSuffix(filename)}`;
}
function ensureHttps(url) {
  if (url.startsWith("http://")) {
    return url.replace("http://", "https://");
  }
  return url;
}
function getFileNameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    if (!path || path === "/" || path.lastIndexOf("/") === path.length - 1) {
      return "";
    }
    const fileName = path.substring(path.lastIndexOf("/") + 1);
    const cleanFileName = fileName.split(/[?#]/)[0];
    return cleanFileName;
  } catch (error) {
    console.error("Invalid URL:", error);
    return "";
  }
}
export {
  genUniqueFilename as a,
  getFileNameFromUrl as b,
  getFileType as c,
  ensureHttps as e,
  getFileNameWithoutExtension as g
};
