import { a as Bridge, g as getDeviceIdWidget, l as logger, B as BridgeZmqUtils, c as setBusinessInfoWidget, r as reportTrackEvent, _ as _export_sfc, d as initLoggerWidget, p as pinia } from "./index.9b9bd033.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
/* empty css                     */import { d as defineComponent, r as ref, z as onMounted, b as openBlock, e as createBlock, f as withCtx, j as createBaseVNode, p as createVNode, u as unref, ad as createApp } from "./bootstrap.ab073eb8.js";
import { C as ClassSummary } from "./index.a0b64fe5.js";
import { _ as __unplugin_components_0, S as SummaryTypeEnum, a as SummaryStatusEnum } from "./aiSummary.4a1241c7.js";
import { g as getAISummaryDetail, a as getAISummaryByid } from "./aiSummary.cdc76542.js";
import { C as CEF_RENDERER_MESSAGE_TYPE, Q as QT_CEF_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import { s as showWarning } from "./toastWidget.501568b8.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./base.676dddc3.js";
import "./index.1c1fd1ce.js";
import "./el-button.a9e8e4ae.js";
import "./use-form-common-props.6b0d7cd2.js";
import "./classroom.421c4d02.js";
import "./axios.b90ffefd.js";
import "./toast.4a39bbb3.js";
import "./index.5cc837e0.js";
import "./crypto-js.7319a219.js";
import "./index.4e3d2b08.js";
import "./event.183fce42.js";
import "./isUndefined.a6a5e481.js";
import "./IResource.516d6004.js";
import "./resource.f585bcf1.js";
const _hoisted_1 = { class: "ai-summarized" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const summaryDialogVisible = ref(true);
    const summaryStatus = ref("");
    const aiClassSummaryInfo = ref(null);
    let aiClassSummaryId = "";
    const serialNumber = ref("");
    const classTeacher = ref();
    const currentResource = ref();
    async function ajaxGetAISummaryByid() {
      const res = await getAISummaryByid({
        aiClassSummaryId
      });
      aiClassSummaryInfo.value = res.data.aiClassSummary;
      logger.info("【ClassSummaryDialog】", "获取AI小结详情", aiClassSummaryInfo.value);
      console.log("AI课堂小结上报结果10S", res);
    }
    async function ajaxGetAISummaryDetail() {
      const { data } = await getAISummaryDetail({
        aiClassSummaryId
      });
      if (data.durationSeconds < 600) {
        summaryStatus.value = SummaryTypeEnum.SHORT_TIME;
        return;
      }
      switch (data.aiSummaryStatus) {
        case SummaryStatusEnum.UNDERWAY:
          summaryStatus.value = SummaryTypeEnum.AI_GENERATING;
          break;
        case SummaryStatusEnum.SUCCEED:
          await ajaxGetAISummaryByid();
          summaryStatus.value = SummaryTypeEnum.AI_SUMMARIZED;
          break;
        case SummaryStatusEnum.DEFEATED:
          summaryStatus.value = SummaryTypeEnum.AI_DEFEATED;
          break;
        case SummaryStatusEnum.NOT_STARTED:
          summaryStatus.value = SummaryTypeEnum.NOT_FINISHED;
          break;
      }
    }
    function onClickSetting() {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.QT_OPEN_CLASSROOM_SETTING_DIALOG);
    }
    function onClose() {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.CLOSE);
    }
    async function onStartSummarize() {
      await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_START_SUMMARY);
      onClose();
    }
    Bridge.getInstance().on(CEF_RENDERER_MESSAGE_TYPE.CLASS_SUMMARY_AI_SUMMARY_ACCOMPLISH, () => {
      ajaxGetAISummaryDetail();
      return {
        message: "处理完成"
      };
    });
    onMounted(async () => {
      try {
        serialNumber.value = await getDeviceIdWidget();
      } catch (e) {
        logger.warn("【ResourceList】获取设备信息失败：", e);
      }
      try {
        await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
          const classTeacher2 = res.teacher || {};
          setBusinessInfoWidget({
            schoolId: classTeacher2.saasSchoolId,
            campusId: classTeacher2.saasCampusId,
            classId: classTeacher2.saasClassId,
            className: classTeacher2.saasClassName,
            subjectCode: classTeacher2.saasSubjectCode,
            subjectName: classTeacher2.saasSubjectName,
            userId: classTeacher2.saasUserId
          });
        });
        reportTrackEvent("CLICK", {
          text: "星讲台-AI课堂小结",
          dataset: {
            text: "点击小章鱼",
            module: "星讲台-AI课堂小结"
          }
        });
      } catch (e) {
        logger.error("【ClassSummaryDialog】", "获取当前上课信息失败", e);
      }
      try {
        const classRoomRes = await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS);
        classTeacher.value = classRoomRes.teacher || {};
        currentResource.value = classRoomRes.currentResource || {};
      } catch (e) {
        logger.warn("【ClassSummaryDialog】", "获取教室信息失败：", e);
      }
      const aiClassSummaryIdRes = await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_GET_AI_CLASS_SUMMARY_ID);
      aiClassSummaryId = aiClassSummaryIdRes.aiClassSummaryId;
      if (!aiClassSummaryId) {
        showWarning("AI小结信息缺失");
        logger.warn("【ClassSummaryDialog】", "aiClassSummaryId为空", aiClassSummaryId);
        onClose();
        return;
      }
      await ajaxGetAISummaryDetail();
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(ClassSummary, {
        "summary-dialog-visible": summaryDialogVisible.value,
        "is-summary-list": false,
        "summary-status": summaryStatus.value,
        "ai-summary-list": aiClassSummaryInfo.value ? [aiClassSummaryInfo.value] : [],
        "ai-class-summary-id": unref(aiClassSummaryId),
        onOpenSettingDialog: onClickSetting,
        onClose,
        onStartSummarize
      }, {
        "ai-summarized": withCtx(({ xmindJsonData, snapshotUrl }) => [
          createBaseVNode("div", _hoisted_1, [
            createVNode(__unplugin_components_0, {
              ref: "xmindRef",
              key: "jsmind_container_ximd",
              "xmind-json-data": xmindJsonData,
              "snapshot-url": snapshotUrl,
              "serial-number": serialNumber.value,
              "class-teacher": classTeacher.value,
              "ai-class-summary-id": unref(aiClassSummaryId),
              "current-resource": currentResource.value,
              "container-id": "jsmind_container_ximd",
              "is-summary-list": false
            }, null, 8, ["xmind-json-data", "snapshot-url", "serial-number", "class-teacher", "ai-class-summary-id", "current-resource"])
          ])
        ]),
        _: 1
      }, 8, ["summary-dialog-visible", "summary-status", "ai-summary-list", "ai-class-summary-id"]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_0c86d137_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-0c86d137"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
