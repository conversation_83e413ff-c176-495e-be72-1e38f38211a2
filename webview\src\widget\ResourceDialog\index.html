<!doctype html><html><head><meta charset="UTF-8"/><title>星讲台</title><script src="../../../libs/track/index.js"></script><script>window._track = new WebTrack({
      env: 'dev',
      config: {
        logstore: 'hl-black-board-client-log'
      },
      watchEvents: {
        /** 目前全埋点监控仅限web端，wx小程序暂未支持 */
        clickMount: true,
        // 开启全埋点监控
        page: false // openTelemetry的page有更详细性能数据，关闭原指标
      },
      openTelemetry: {
        // 开启openTelemetry
        fetch: true,
        // 接口trace
        xhr: true,
        // 接口trace
        page: true,
        // 页面性能统计trace
        userInteraction: true // 用户行为trace
      },
      globalData: {
        _project_: 'black-board-client',
        _project_name_: '星讲台'
      },
      terminal: 'windows',
      // 自定义过滤
      filterRule: function(data) {
        try {
          var eventCode = data._event_code_;
          var eventInfo = data._event_info_
          switch (eventCode) {
            case 'CLICK':
              if (!eventInfo) {return true}
              if (typeof eventInfo === 'string') {
                eventInfo = JSON.parse(eventInfo);
              }
              // 点击事件，如果text和module都为空，则不发送埋点数据
              var isOuterText = eventInfo.text;
              var isInnerText;
              var isInnerModule;
              if (eventInfo.dataset) {
                isInnerText = eventInfo.dataset.text;
                isInnerModule = eventInfo.dataset.module;
              }
              return !isOuterText && !isInnerText && !isInnerModule;
          }
          return false;
        } catch {
          return false;
        }
      }
    });</script><script type="module" crossorigin src="../../../js/ResourceDialog.b6fcff82.js"></script><link rel="modulepreload" crossorigin href="../../../js/encryptlong.f30353e7.js"><link rel="modulepreload" crossorigin href="../../../js/bootstrap.ab073eb8.js"><link rel="modulepreload" crossorigin href="../../../js/base.649d38c6.js"><link rel="modulepreload" crossorigin href="../../../js/index.9b9bd033.js"><link rel="modulepreload" crossorigin href="../../../js/rem.5d1b1196.js"><link rel="modulepreload" crossorigin href="../../../js/index.1c1fd1ce.js"><link rel="modulepreload" crossorigin href="../../../js/index.382e5561.js"><link rel="modulepreload" crossorigin href="../../../js/base.676dddc3.js"><link rel="modulepreload" crossorigin href="../../../js/index.4d07c967.js"><link rel="modulepreload" crossorigin href="../../../js/index.4e3d2b08.js"><link rel="modulepreload" crossorigin href="../../../js/typescript.063380fa.js"><link rel="modulepreload" crossorigin href="../../../js/use-form-common-props.6b0d7cd2.js"><link rel="modulepreload" crossorigin href="../../../js/isUndefined.a6a5e481.js"><link rel="modulepreload" crossorigin href="../../../js/hlwhiteboard.b54f17ff.js"><link rel="modulepreload" crossorigin href="../../../js/index.8e8e3f71.js"><link rel="modulepreload" crossorigin href="../../../js/el-popover.f265f19e.js"><link rel="modulepreload" crossorigin href="../../../js/index.5cc837e0.js"><link rel="modulepreload" crossorigin href="../../../js/event.183fce42.js"><link rel="modulepreload" crossorigin href="../../../js/el-button.a9e8e4ae.js"><link rel="modulepreload" crossorigin href="../../../js/IResource.516d6004.js"><link rel="modulepreload" crossorigin href="../../../js/toast.4a39bbb3.js"><link rel="modulepreload" crossorigin href="../../../js/crypto-js.7319a219.js"><link rel="modulepreload" crossorigin href="../../../js/toastWidget.501568b8.js"><link rel="modulepreload" crossorigin href="../../../js/axios.b90ffefd.js"><link rel="modulepreload" crossorigin href="../../../js/file.ce032cfb.js"><link rel="modulepreload" crossorigin href="../../../js/ossUtils.8762c65d.js"><link rel="modulepreload" crossorigin href="../../../js/position.2e4d825a.js"><link rel="modulepreload" crossorigin href="../../../js/el-image-viewer.10cb6477.js"><link rel="modulepreload" crossorigin href="../../../js/resource.f585bcf1.js"><link rel="modulepreload" crossorigin href="../../../js/<EMAIL>"><link rel="modulepreload" crossorigin href="../../../js/image.77fe4778.js"><link rel="modulepreload" crossorigin href="../../../js/bury.6b12311b.js"><link rel="modulepreload" crossorigin href="../../../js/IComm.f4ebabd4.js"><link rel="stylesheet" href="../../../css/index.046829b5.css"><link rel="stylesheet" href="../../../css/index.cff54096.css"><link rel="stylesheet" href="../../../css/base.36af3b57.css"><link rel="stylesheet" href="../../../css/index.c7438828.css"><link rel="stylesheet" href="../../../css/el-popover.3580f22f.css"><link rel="stylesheet" href="../../../css/index.87cec2d4.css"><link rel="stylesheet" href="../../../css/el-button.1f189f69.css"><link rel="stylesheet" href="../../../css/ossUtils.fd1cea13.css"><link rel="stylesheet" href="../../../css/el-image-viewer.5069c0dd.css"><link rel="stylesheet" href="../../../css/index.cbfb4f87.css"></head><body><div id="app"></div><script src="../../../libs/iconfont.js?asset"></script></body></html>